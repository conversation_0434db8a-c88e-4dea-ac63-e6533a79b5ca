#!/usr/bin/env python3
"""
自适应睡眠机制测试脚本
用于验证睡眠时间随运行时间增加的效果
"""

import time
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta

def calculate_adaptive_sleep_time(elapsed_time, initial_sleep_time=0.1, target_sleep_time=9.0, ramp_up_duration=3600.0):
    """
    计算自适应睡眠时间（与主程序中的函数相同，优化版）

    参数:
        elapsed_time: 已运行时间（秒）
        initial_sleep_time: 初始睡眠时间（秒）
        target_sleep_time: 目标睡眠时间（秒）
        ramp_up_duration: 渐增时间（秒）

    返回:
        float: 计算出的睡眠时间（秒）
    """
    # 如果运行时间超过渐增时间，直接返回目标睡眠时间
    if elapsed_time >= ramp_up_duration:
        return target_sleep_time

    # 前30分钟保持较快频率的优化策略
    fast_response_duration = 1800.0  # 前30分钟 = 1800秒
    fast_response_sleep_time = 0.5   # 前30分钟的睡眠时间

    # 如果在前30分钟内，保持较快的睡眠时间
    if elapsed_time <= fast_response_duration:
        # 在前30分钟内，从0.1秒缓慢增长到0.5秒
        progress_fast = elapsed_time / fast_response_duration
        current_sleep_time = initial_sleep_time + (fast_response_sleep_time - initial_sleep_time) * progress_fast
        return current_sleep_time

    # 30分钟后，从0.5秒增长到9秒（剩余30分钟）
    remaining_time = elapsed_time - fast_response_duration
    remaining_duration = ramp_up_duration - fast_response_duration  # 剩余30分钟

    # 计算在剩余时间段的进度
    progress_slow = remaining_time / remaining_duration  # 进度比例 [0, 1]

    # 使用S型曲线进行后半段的渐增（从0.5秒到9秒）
    k = 2.5  # 降低k值，使曲线更平缓
    normalized_progress = (1 - np.exp(-k * progress_slow)) / (1 - np.exp(-k))

    # 计算当前睡眠时间（从fast_response_sleep_time到target_sleep_time）
    current_sleep_time = fast_response_sleep_time + (target_sleep_time - fast_response_sleep_time) * normalized_progress

    return current_sleep_time


def test_adaptive_sleep_curve():
    """测试自适应睡眠曲线"""
    print("=== 自适应睡眠机制测试（优化版）===")
    print()

    # 测试参数
    initial_sleep = 0.1
    target_sleep = 9.0
    ramp_duration = 3600.0  # 1小时

    print(f"初始睡眠时间: {initial_sleep}秒")
    print(f"前30分钟睡眠时间: 0.1-0.5秒 (保持快速响应)")
    print(f"后30分钟睡眠时间: 0.5-{target_sleep}秒 (平滑过渡)")
    print(f"目标睡眠时间: {target_sleep}秒")
    print(f"总渐增时间: {ramp_duration/3600:.1f}小时")
    print()
    
    # 生成测试时间点（0到2小时，每5分钟一个点）
    time_points = np.arange(0, 7200, 300)  # 0到2小时，每5分钟
    sleep_times = []
    
    print("时间点测试结果:")
    print(f"{'运行时间':<12} {'睡眠时间':<10} {'增长率':<10} {'说明'}")
    print("-" * 50)
    
    prev_sleep = initial_sleep
    for t in time_points:
        sleep_time = calculate_adaptive_sleep_time(t, initial_sleep, target_sleep, ramp_duration)
        sleep_times.append(sleep_time)
        
        # 计算增长率
        growth_rate = ((sleep_time - prev_sleep) / prev_sleep * 100) if prev_sleep > 0 else 0
        
        # 格式化时间显示
        hours = int(t // 3600)
        minutes = int((t % 3600) // 60)
        time_str = f"{hours:02d}:{minutes:02d}"
        
        # 添加说明
        if t == 0:
            note = "启动时"
        elif t == 1800:  # 30分钟
            note = "30分钟"
        elif t == 3600:  # 1小时
            note = "1小时(目标)"
        elif t >= 3600:
            note = "稳定期"
        else:
            note = ""
        
        print(f"{time_str:<12} {sleep_time:<10.2f} {growth_rate:<10.1f}% {note}")
        prev_sleep = sleep_time
    
    print()
    
    # 关键时间点分析
    print("关键时间点分析:")
    key_times = [0, 300, 600, 1800, 3600, 5400, 7200]  # 0, 5min, 10min, 30min, 1h, 1.5h, 2h
    
    for t in key_times:
        sleep_time = calculate_adaptive_sleep_time(t, initial_sleep, target_sleep, ramp_duration)
        hours = t / 3600
        percentage = (sleep_time - initial_sleep) / (target_sleep - initial_sleep) * 100
        
        print(f"运行{hours:4.1f}小时: 睡眠时间{sleep_time:5.2f}秒 (达到目标的{percentage:5.1f}%)")
    
    return time_points, sleep_times


def simulate_program_behavior():
    """模拟程序运行行为"""
    print("\n=== 程序运行行为模拟 ===")
    
    # 模拟2小时的运行
    simulation_duration = 7200  # 2小时
    current_time = 0
    cycle_count = 0
    total_cycles_per_hour = []
    
    print("模拟程序运行，计算每小时的循环次数变化...")
    
    hour_start = 0
    cycles_in_hour = 0
    
    while current_time < simulation_duration:
        # 计算当前睡眠时间
        sleep_time = calculate_adaptive_sleep_time(current_time)
        
        # 模拟循环执行时间（假设平均0.5秒）
        loop_execution_time = 0.5
        
        # 总循环时间
        total_cycle_time = loop_execution_time + sleep_time
        
        # 更新时间和计数
        current_time += total_cycle_time
        cycle_count += 1
        cycles_in_hour += 1
        
        # 每小时统计一次
        if current_time - hour_start >= 3600:
            hour_num = int(hour_start // 3600) + 1
            cycles_per_hour = cycles_in_hour * (3600 / (current_time - hour_start))
            total_cycles_per_hour.append(cycles_per_hour)
            
            print(f"第{hour_num}小时: 约{cycles_per_hour:.0f}次循环 (平均间隔{3600/cycles_per_hour:.1f}秒)")
            
            hour_start = current_time
            cycles_in_hour = 0
    
    print(f"\n总循环次数: {cycle_count}")
    print(f"平均每小时循环次数: {np.mean(total_cycles_per_hour):.0f}")
    print(f"循环频率降低: {(total_cycles_per_hour[0] - total_cycles_per_hour[-1]) / total_cycles_per_hour[0] * 100:.1f}%")


def generate_visualization():
    """生成可视化图表"""
    print("\n=== 生成可视化图表 ===")
    
    try:
        # 生成数据
        time_points, sleep_times = test_adaptive_sleep_curve()
        
        # 转换时间为小时
        time_hours = np.array(time_points) / 3600
        
        # 创建图表
        plt.figure(figsize=(12, 8))
        
        # 子图1: 睡眠时间曲线
        plt.subplot(2, 1, 1)
        plt.plot(time_hours, sleep_times, 'b-', linewidth=2, label='睡眠时间')
        plt.axhline(y=9.0, color='r', linestyle='--', alpha=0.7, label='目标睡眠时间(9秒)')
        plt.axvline(x=1.0, color='g', linestyle='--', alpha=0.7, label='1小时标记')
        plt.xlabel('运行时间 (小时)')
        plt.ylabel('睡眠时间 (秒)')
        plt.title('自适应睡眠时间变化曲线')
        plt.grid(True, alpha=0.3)
        plt.legend()
        
        # 子图2: 循环频率变化
        plt.subplot(2, 1, 2)
        # 计算每小时的循环次数（假设循环执行时间0.5秒）
        cycle_frequencies = 3600 / (0.5 + np.array(sleep_times))
        plt.plot(time_hours, cycle_frequencies, 'g-', linewidth=2, label='每小时循环次数')
        plt.xlabel('运行时间 (小时)')
        plt.ylabel('每小时循环次数')
        plt.title('循环频率变化')
        plt.grid(True, alpha=0.3)
        plt.legend()
        
        plt.tight_layout()
        plt.savefig('adaptive_sleep_analysis.png', dpi=300, bbox_inches='tight')
        print("图表已保存为 'adaptive_sleep_analysis.png'")
        
    except ImportError:
        print("matplotlib未安装，跳过图表生成")
        print("如需生成图表，请安装: pip install matplotlib")


def main():
    """主测试函数"""
    print("自适应睡眠机制测试开始...")
    print("=" * 60)
    
    # 测试睡眠曲线
    test_adaptive_sleep_curve()
    
    # 模拟程序行为
    simulate_program_behavior()
    
    # 生成可视化（如果可能）
    generate_visualization()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("\n关键特性:")
    print("1. 程序启动时睡眠时间为0.1秒，保证快速响应")
    print("2. 睡眠时间使用S型曲线平滑增长")
    print("3. 1小时后睡眠时间稳定在9秒左右")
    print("4. 循环频率从初期的每小时约7200次降低到约400次")
    print("5. 有效减少长期运行时的系统负载")


if __name__ == "__main__":
    main()
