import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class PriceSmoothingOptimizer:
    def __init__(self, max_change_pct=0.05, smoothing_window=5):
        """
        价格平滑优化器
        Args:
            max_change_pct: 最大允许的价格变化百分比 (默认5%)
            smoothing_window: 平滑窗口大小
        """
        self.max_change_pct = max_change_pct
        self.smoothing_window = smoothing_window
        self.optimization_log = []
    
    def detect_price_anomalies(self, df):
        """检测价格异常"""
        anomalies = []
        
        for i in range(1, len(df)):
            prev_close = df.iloc[i-1]['close']
            current_open = df.iloc[i]['open']
            current_close = df.iloc[i]['close']
            current_high = df.iloc[i]['high']
            current_low = df.iloc[i]['low']
            
            # 检测开盘价跳空
            open_gap = abs(current_open - prev_close) / prev_close
            if open_gap > self.max_change_pct:
                anomalies.append({
                    'index': i,
                    'type': '开盘跳空',
                    'severity': open_gap,
                    'prev_close': prev_close,
                    'current_open': current_open
                })
            
            # 检测单根K线内部的极端波动
            if current_high > 0 and current_low > 0:
                intra_volatility = (current_high - current_low) / current_low
                if intra_volatility > self.max_change_pct * 2:  # 单根K线波动超过10%
                    anomalies.append({
                        'index': i,
                        'type': '单根K线极端波动',
                        'severity': intra_volatility,
                        'high': current_high,
                        'low': current_low
                    })
            
            # 检测收盘价异常
            close_change = abs(current_close - prev_close) / prev_close
            if close_change > self.max_change_pct:
                anomalies.append({
                    'index': i,
                    'type': '收盘价异常',
                    'severity': close_change,
                    'prev_close': prev_close,
                    'current_close': current_close
                })
        
        return anomalies
    
    def smooth_price_series(self, prices, method='ema'):
        """平滑价格序列"""
        if method == 'ema':
            # 指数移动平均
            alpha = 2 / (self.smoothing_window + 1)
            smoothed = [prices[0]]
            for i in range(1, len(prices)):
                smoothed.append(alpha * prices[i] + (1 - alpha) * smoothed[-1])
            return np.array(smoothed)
        elif method == 'sma':
            # 简单移动平均
            return prices.rolling(window=self.smoothing_window, center=True).mean().fillna(method='bfill').fillna(method='ffill')
        elif method == 'median':
            # 中位数滤波
            return prices.rolling(window=self.smoothing_window, center=True).median().fillna(method='bfill').fillna(method='ffill')
    
    def optimize_ohlc_consistency(self, df):
        """确保OHLC数据的一致性"""
        df_optimized = df.copy()
        
        for i in range(len(df_optimized)):
            open_price = df_optimized.iloc[i]['open']
            close_price = df_optimized.iloc[i]['close']
            high_price = df_optimized.iloc[i]['high']
            low_price = df_optimized.iloc[i]['low']
            
            # 确保high >= max(open, close) 且 low <= min(open, close)
            corrected_high = max(high_price, open_price, close_price)
            corrected_low = min(low_price, open_price, close_price)
            
            df_optimized.iloc[i, df_optimized.columns.get_loc('high')] = corrected_high
            df_optimized.iloc[i, df_optimized.columns.get_loc('low')] = corrected_low
        
        return df_optimized
    
    def apply_price_smoothing(self, df):
        """应用价格平滑"""
        df_smoothed = df.copy()
        
        # 对收盘价进行平滑
        close_smoothed = self.smooth_price_series(df['close'], method='ema')
        
        # 基于平滑后的收盘价调整其他价格
        for i in range(len(df_smoothed)):
            original_close = df.iloc[i]['close']
            smoothed_close = close_smoothed[i]
            
            # 计算调整比例
            if original_close > 0:
                adjustment_ratio = smoothed_close / original_close
                
                # 调整开盘价、最高价、最低价
                df_smoothed.iloc[i, df_smoothed.columns.get_loc('open')] *= adjustment_ratio
                df_smoothed.iloc[i, df_smoothed.columns.get_loc('high')] *= adjustment_ratio
                df_smoothed.iloc[i, df_smoothed.columns.get_loc('low')] *= adjustment_ratio
                df_smoothed.iloc[i, df_smoothed.columns.get_loc('close')] = smoothed_close
        
        return df_smoothed
    
    def optimize_extreme_gaps(self, df):
        """优化极端跳空"""
        df_optimized = df.copy()
        
        for i in range(1, len(df_optimized)):
            prev_close = df_optimized.iloc[i-1]['close']
            current_open = df_optimized.iloc[i]['open']
            
            # 计算跳空幅度
            gap_pct = abs(current_open - prev_close) / prev_close
            
            if gap_pct > self.max_change_pct:
                # 限制跳空幅度
                max_change = prev_close * self.max_change_pct
                if current_open > prev_close:
                    # 向上跳空
                    optimized_open = prev_close + max_change
                else:
                    # 向下跳空
                    optimized_open = prev_close - max_change
                
                # 记录优化信息
                self.optimization_log.append({
                    'index': i,
                    'timestamp': df_optimized.iloc[i]['timestamp'],
                    'type': '跳空优化',
                    'original_open': current_open,
                    'optimized_open': optimized_open,
                    'gap_reduction': abs(current_open - prev_close) - abs(optimized_open - prev_close)
                })
                
                # 应用优化
                adjustment_ratio = optimized_open / current_open
                df_optimized.iloc[i, df_optimized.columns.get_loc('open')] = optimized_open
                df_optimized.iloc[i, df_optimized.columns.get_loc('close')] *= adjustment_ratio
                df_optimized.iloc[i, df_optimized.columns.get_loc('high')] *= adjustment_ratio
                df_optimized.iloc[i, df_optimized.columns.get_loc('low')] *= adjustment_ratio
        
        return df_optimized
    
    def optimize_kline_data(self, df):
        """完整的K线数据优化流程"""
        print("开始K线数据平滑优化...")
        
        # 1. 检测异常
        anomalies = self.detect_price_anomalies(df)
        print(f"检测到 {len(anomalies)} 个价格异常")
        
        # 2. 优化极端跳空
        df_step1 = self.optimize_extreme_gaps(df)
        print(f"优化了 {len(self.optimization_log)} 个极端跳空")
        
        # 3. 应用价格平滑
        df_step2 = self.apply_price_smoothing(df_step1)
        print("应用价格平滑完成")
        
        # 4. 确保OHLC一致性
        df_final = self.optimize_ohlc_consistency(df_step2)
        print("OHLC一致性检查完成")
        
        return df_final, anomalies
    
    def generate_comparison_chart(self, df_original, df_optimized):
        """生成对比图表"""
        fig, axes = plt.subplots(3, 1, figsize=(16, 12), sharex=True)
        
        # 转换时间戳
        df_original['datetime'] = pd.to_datetime(df_original['timestamp'], unit='s')
        df_optimized['datetime'] = pd.to_datetime(df_optimized['timestamp'], unit='s')
        
        # 1. 价格对比
        axes[0].plot(df_original['datetime'], df_original['close'], 
                    label='原始收盘价', color='red', alpha=0.7, linewidth=1)
        axes[0].plot(df_optimized['datetime'], df_optimized['close'], 
                    label='平滑后收盘价', color='blue', linewidth=2)
        axes[0].set_title('价格平滑对比', fontsize=14, fontweight='bold')
        axes[0].set_ylabel('价格 (USDT)')
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)
        
        # 2. 波动率对比
        original_returns = df_original['close'].pct_change().abs()
        optimized_returns = df_optimized['close'].pct_change().abs()
        
        axes[1].plot(df_original['datetime'][1:], original_returns[1:], 
                    label='原始波动率', color='red', alpha=0.7)
        axes[1].plot(df_optimized['datetime'][1:], optimized_returns[1:], 
                    label='平滑后波动率', color='blue')
        axes[1].set_title('波动率对比', fontsize=14, fontweight='bold')
        axes[1].set_ylabel('绝对收益率')
        axes[1].legend()
        axes[1].grid(True, alpha=0.3)
        
        # 3. 价格范围对比
        original_range = df_original['high'] - df_original['low']
        optimized_range = df_optimized['high'] - df_optimized['low']
        
        axes[2].plot(df_original['datetime'], original_range, 
                    label='原始价格范围', color='red', alpha=0.7)
        axes[2].plot(df_optimized['datetime'], optimized_range, 
                    label='平滑后价格范围', color='blue')
        axes[2].set_title('单根K线价格范围对比', fontsize=14, fontweight='bold')
        axes[2].set_ylabel('价格范围 (USDT)')
        axes[2].set_xlabel('时间')
        axes[2].legend()
        axes[2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图表
        filename = f'bybusdt_price_smoothing_comparison_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"对比图表已保存为: {filename}")
        
        plt.show()
        return filename

def main():
    """主函数"""
    try:
        # 读取K线数据
        print("正在读取K线数据...")
        df = pd.read_csv('bybusdt_1min_kline.csv')
        print(f"成功读取 {len(df)} 条K线数据")
        
        # 数据预处理
        df['timestamp'] = df['timestamp']  # 假设已经是时间戳格式
        
        # 创建优化器
        optimizer = PriceSmoothingOptimizer(
            max_change_pct=0.03,  # 限制单次价格变化不超过3%
            smoothing_window=7    # 使用7周期平滑
        )
        
        # 执行优化
        df_optimized, anomalies = optimizer.optimize_kline_data(df)
        
        # 生成对比图表
        chart_file = optimizer.generate_comparison_chart(df, df_optimized)
        
        # 输出优化统计
        print("\n=== 价格平滑优化报告 ===")
        
        # 原始数据统计
        original_volatility = df['close'].pct_change().std()
        original_max_change = df['close'].pct_change().abs().max()
        original_range = df['high'].max() - df['low'].min()
        
        # 优化后数据统计
        optimized_volatility = df_optimized['close'].pct_change().std()
        optimized_max_change = df_optimized['close'].pct_change().abs().max()
        optimized_range = df_optimized['high'].max() - df_optimized['low'].min()
        
        print(f"原始数据:")
        print(f"  价格波动率: {original_volatility:.6f}")
        print(f"  最大单次变化: {original_max_change:.4f} ({original_max_change*100:.2f}%)")
        print(f"  价格总范围: {original_range:.6f} USDT")
        
        print(f"\n优化后数据:")
        print(f"  价格波动率: {optimized_volatility:.6f}")
        print(f"  最大单次变化: {optimized_max_change:.4f} ({optimized_max_change*100:.2f}%)")
        print(f"  价格总范围: {optimized_range:.6f} USDT")
        
        print(f"\n优化效果:")
        volatility_reduction = (original_volatility - optimized_volatility) / original_volatility * 100
        max_change_reduction = (original_max_change - optimized_max_change) / original_max_change * 100
        range_reduction = (original_range - optimized_range) / original_range * 100
        
        print(f"  波动率降低: {volatility_reduction:.2f}%")
        print(f"  最大变化降低: {max_change_reduction:.2f}%")
        print(f"  价格范围缩小: {range_reduction:.2f}%")
        
        # 显示优化详情
        if optimizer.optimization_log:
            print(f"\n=== 跳空优化详情 ===")
            for i, log in enumerate(optimizer.optimization_log[:10], 1):  # 只显示前10个
                timestamp = datetime.fromtimestamp(log['timestamp'])
                print(f"{i}. 时间: {timestamp}")
                print(f"   原始开盘: {log['original_open']:.6f} -> 优化后: {log['optimized_open']:.6f}")
                print(f"   跳空减少: {log['gap_reduction']:.6f} USDT")
        
        # 保存优化后的数据
        output_file = 'bybusdt_1min_kline_smoothed.csv'
        df_optimized.to_csv(output_file, index=False)
        print(f"\n平滑优化后的K线数据已保存为: {output_file}")
        
        # 生成最终统计
        print(f"\n=== 最终数据质量评估 ===")
        large_moves = (df_optimized['close'].pct_change().abs() > 0.02).sum()
        print(f"超过2%的价格变动: {large_moves} 次")
        
        if large_moves < len(df_optimized) * 0.05:  # 少于5%的数据点
            print("✓ 数据平滑度良好，适合技术分析")
        else:
            print("⚠ 仍存在较多大幅波动，可考虑进一步优化")
        
    except FileNotFoundError:
        print("错误: 未找到文件 'bybusdt_1min_kline.csv'")
        print("请确保文件在当前目录下")
    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
