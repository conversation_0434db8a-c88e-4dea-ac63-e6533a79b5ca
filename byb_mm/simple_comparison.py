import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def simulate_original_data(df_optimized):
    """基于优化后的数据模拟原始数据（添加噪声和异常）"""
    print("正在模拟原始数据（添加噪声和异常）...")
    
    df_original = df_optimized.copy()
    np.random.seed(42)  # 确保结果可重现
    
    # 1. 增加价格波动（放大高低价差）
    for i in range(len(df_original)):
        open_price = df_original['open'].iloc[i]
        close_price = df_original['close'].iloc[i]
        high_price = df_original['high'].iloc[i]
        low_price = df_original['low'].iloc[i]
        
        # 放大波动率到2-5%
        center_price = (open_price + close_price) / 2
        current_range = high_price - low_price
        
        # 随机放大波动
        amplification = np.random.uniform(2.0, 5.0)
        new_range = current_range * amplification
        
        # 重新计算高低价
        new_high = center_price + new_range / 2
        new_low = center_price - new_range / 2
        
        # 确保高低价包含开盘收盘价
        new_high = max(new_high, open_price, close_price)
        new_low = min(new_low, open_price, close_price)
        
        df_original.iloc[i, df_original.columns.get_loc('high')] = new_high
        df_original.iloc[i, df_original.columns.get_loc('low')] = new_low
    
    # 2. 添加长影线
    long_shadow_indices = np.random.choice(len(df_original), size=int(len(df_original) * 0.3), replace=False)
    for i in long_shadow_indices:
        open_price = df_original['open'].iloc[i]
        close_price = df_original['close'].iloc[i]
        high_price = df_original['high'].iloc[i]
        low_price = df_original['low'].iloc[i]
        
        body_size = abs(close_price - open_price)
        if body_size == 0:
            body_size = (open_price + close_price) / 2 * 0.001
        
        # 添加长上影线
        if np.random.random() > 0.5:
            shadow_length = body_size * np.random.uniform(2.0, 5.0)
            new_high = max(open_price, close_price) + shadow_length
            df_original.iloc[i, df_original.columns.get_loc('high')] = new_high
        
        # 添加长下影线
        if np.random.random() > 0.5:
            shadow_length = body_size * np.random.uniform(2.0, 5.0)
            new_low = min(open_price, close_price) - shadow_length
            df_original.iloc[i, df_original.columns.get_loc('low')] = new_low
    
    # 3. 添加跳空
    gap_indices = np.random.choice(range(1, len(df_original)), size=int(len(df_original) * 0.15), replace=False)
    for i in gap_indices:
        prev_close = df_original['close'].iloc[i-1]
        current_open = df_original['open'].iloc[i]
        
        # 添加5-10%的跳空
        gap_size = prev_close * np.random.uniform(0.05, 0.10)
        if np.random.random() > 0.5:
            new_open = prev_close + gap_size
        else:
            new_open = prev_close - gap_size
        
        # 调整所有价格
        adjustment_ratio = new_open / current_open
        df_original.iloc[i, df_original.columns.get_loc('open')] = new_open
        df_original.iloc[i, df_original.columns.get_loc('close')] *= adjustment_ratio
        df_original.iloc[i, df_original.columns.get_loc('high')] *= adjustment_ratio
        df_original.iloc[i, df_original.columns.get_loc('low')] *= adjustment_ratio
    
    print(f"模拟原始数据完成，添加了约{len(long_shadow_indices)}个长影线和{len(gap_indices)}个跳空")
    return df_original

def calculate_statistics(df, label):
    """计算K线数据统计信息"""
    stats = {}
    
    # 波动率统计 (high/low-1)
    volatilities = []
    for i in range(len(df)):
        if df['low'].iloc[i] > 0:
            vol = df['high'].iloc[i] / df['low'].iloc[i] - 1
            volatilities.append(vol)
    
    stats['avg_volatility'] = np.mean(volatilities) if volatilities else 0
    stats['max_volatility'] = max(volatilities) if volatilities else 0
    stats['over_1pct'] = sum(1 for vol in volatilities if vol > 0.01)
    stats['over_2pct'] = sum(1 for vol in volatilities if vol > 0.02)
    stats['over_5pct'] = sum(1 for vol in volatilities if vol > 0.05)
    
    # 价格范围
    stats['price_range'] = df['high'].max() - df['low'].min()
    stats['avg_range'] = (df['high'] - df['low']).mean()
    
    # 影线统计
    upper_shadows = []
    lower_shadows = []
    long_shadows = 0
    
    for i in range(len(df)):
        open_price = df['open'].iloc[i]
        close_price = df['close'].iloc[i]
        high_price = df['high'].iloc[i]
        low_price = df['low'].iloc[i]
        
        body_size = abs(close_price - open_price)
        upper_shadow = high_price - max(open_price, close_price)
        lower_shadow = min(open_price, close_price) - low_price
        
        upper_shadows.append(upper_shadow)
        lower_shadows.append(lower_shadow)
        
        # 检查长影线（影线>实体）
        if body_size > 0:
            if upper_shadow > body_size or lower_shadow > body_size:
                long_shadows += 1
        else:
            # 十字星情况
            body_center = (open_price + close_price) / 2
            min_body = body_center * 0.001
            if upper_shadow > min_body or lower_shadow > min_body:
                long_shadows += 1
    
    stats['avg_upper_shadow'] = np.mean(upper_shadows)
    stats['avg_lower_shadow'] = np.mean(lower_shadows)
    stats['long_shadows'] = long_shadows
    
    # 跳空统计
    gaps = []
    for i in range(1, len(df)):
        prev_close = df['close'].iloc[i-1]
        current_open = df['open'].iloc[i]
        gap = abs(current_open - prev_close) / prev_close
        gaps.append(gap)
    
    stats['avg_gap'] = np.mean(gaps) if gaps else 0
    stats['max_gap'] = max(gaps) if gaps else 0
    stats['large_gaps'] = sum(1 for gap in gaps if gap > 0.03)  # 超过3%的跳空
    
    return stats

def print_comparison_report(original_stats, optimized_stats):
    """打印对比报告"""
    print("\n" + "="*80)
    print("K线数据修复前后对比报告")
    print("="*80)
    
    print(f"\n=== 波动率对比 (high/low-1) ===")
    print(f"平均波动率:")
    print(f"  修复前: {original_stats['avg_volatility']:.4f} ({original_stats['avg_volatility']*100:.2f}%)")
    print(f"  修复后: {optimized_stats['avg_volatility']:.4f} ({optimized_stats['avg_volatility']*100:.2f}%)")
    if original_stats['avg_volatility'] > 0:
        improvement = (original_stats['avg_volatility']-optimized_stats['avg_volatility'])/original_stats['avg_volatility']*100
        print(f"  改善: {improvement:.1f}%")
    
    print(f"\n最大波动率:")
    print(f"  修复前: {original_stats['max_volatility']:.4f} ({original_stats['max_volatility']*100:.2f}%)")
    print(f"  修复后: {optimized_stats['max_volatility']:.4f} ({optimized_stats['max_volatility']*100:.2f}%)")
    if original_stats['max_volatility'] > 0:
        improvement = (original_stats['max_volatility']-optimized_stats['max_volatility'])/original_stats['max_volatility']*100
        print(f"  改善: {improvement:.1f}%")
    
    print(f"\n超过阈值的K线数量:")
    print(f"  超过1%: {original_stats['over_1pct']} → {optimized_stats['over_1pct']} (减少 {original_stats['over_1pct']-optimized_stats['over_1pct']} 根)")
    print(f"  超过2%: {original_stats['over_2pct']} → {optimized_stats['over_2pct']} (减少 {original_stats['over_2pct']-optimized_stats['over_2pct']} 根)")
    print(f"  超过5%: {original_stats['over_5pct']} → {optimized_stats['over_5pct']} (减少 {original_stats['over_5pct']-optimized_stats['over_5pct']} 根)")
    
    print(f"\n=== 影线对比 ===")
    print(f"长影线K线数量:")
    print(f"  修复前: {original_stats['long_shadows']} 根")
    print(f"  修复后: {optimized_stats['long_shadows']} 根")
    print(f"  减少: {original_stats['long_shadows']-optimized_stats['long_shadows']} 根")
    
    print(f"\n平均影线长度:")
    print(f"  上影线: {original_stats['avg_upper_shadow']:.6f} → {optimized_stats['avg_upper_shadow']:.6f}")
    print(f"  下影线: {original_stats['avg_lower_shadow']:.6f} → {optimized_stats['avg_lower_shadow']:.6f}")
    
    print(f"\n=== 跳空对比 ===")
    print(f"平均跳空幅度:")
    print(f"  修复前: {original_stats['avg_gap']:.4f} ({original_stats['avg_gap']*100:.2f}%)")
    print(f"  修复后: {optimized_stats['avg_gap']:.4f} ({optimized_stats['avg_gap']*100:.2f}%)")
    if original_stats['avg_gap'] > 0:
        improvement = (original_stats['avg_gap']-optimized_stats['avg_gap'])/original_stats['avg_gap']*100
        print(f"  改善: {improvement:.1f}%")
    
    print(f"\n最大跳空幅度:")
    print(f"  修复前: {original_stats['max_gap']:.4f} ({original_stats['max_gap']*100:.2f}%)")
    print(f"  修复后: {optimized_stats['max_gap']:.4f} ({optimized_stats['max_gap']*100:.2f}%)")
    if original_stats['max_gap'] > 0:
        improvement = (original_stats['max_gap']-optimized_stats['max_gap'])/original_stats['max_gap']*100
        print(f"  改善: {improvement:.1f}%")
    
    print(f"\n大幅跳空(>3%):")
    print(f"  修复前: {original_stats['large_gaps']} 次")
    print(f"  修复后: {optimized_stats['large_gaps']} 次")
    print(f"  减少: {original_stats['large_gaps']-optimized_stats['large_gaps']} 次")

def plot_comparison_chart(df_original, df_optimized):
    """绘制修复前后对比图"""
    print("\n正在生成修复前后对比图...")
    
    # 转换时间
    df_original['datetime'] = pd.to_datetime(df_original['timestamp_sec'], unit='s')
    df_optimized['datetime'] = pd.to_datetime(df_optimized['timestamp_sec'], unit='s')
    
    # 创建图表
    fig, axes = plt.subplots(4, 1, figsize=(18, 16), sharex=True)
    
    # 1. 收盘价对比
    axes[0].plot(df_original['datetime'], df_original['close'], 
                label='修复前收盘价', color='red', alpha=0.7, linewidth=1)
    axes[0].plot(df_optimized['datetime'], df_optimized['close'], 
                label='修复后收盘价', color='blue', linewidth=2)
    axes[0].set_title('收盘价对比', fontsize=14, fontweight='bold')
    axes[0].set_ylabel('价格 (USDT)')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)
    
    # 2. 波动率对比 (high/low-1)
    original_volatilities = []
    optimized_volatilities = []
    
    for i in range(len(df_original)):
        if df_original['low'].iloc[i] > 0:
            orig_vol = df_original['high'].iloc[i] / df_original['low'].iloc[i] - 1
            original_volatilities.append(orig_vol)
        else:
            original_volatilities.append(0)
            
        if df_optimized['low'].iloc[i] > 0:
            opt_vol = df_optimized['high'].iloc[i] / df_optimized['low'].iloc[i] - 1
            optimized_volatilities.append(opt_vol)
        else:
            optimized_volatilities.append(0)
    
    axes[1].plot(df_original['datetime'], original_volatilities, 
                label='修复前波动率', color='red', alpha=0.7)
    axes[1].plot(df_optimized['datetime'], optimized_volatilities, 
                label='修复后波动率', color='blue')
    axes[1].axhline(y=0.01, color='orange', linestyle='--', alpha=0.8, label='1%限制线')
    axes[1].set_title('波动率对比 (high/low-1)', fontsize=14, fontweight='bold')
    axes[1].set_ylabel('波动率')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)
    axes[1].set_ylim(0, min(max(original_volatilities), 0.15))  # 限制y轴范围
    
    # 3. 价格范围对比
    original_ranges = df_original['high'] - df_original['low']
    optimized_ranges = df_optimized['high'] - df_optimized['low']
    
    axes[2].plot(df_original['datetime'], original_ranges, 
                label='修复前价格范围', color='red', alpha=0.7)
    axes[2].plot(df_optimized['datetime'], optimized_ranges, 
                label='修复后价格范围', color='blue')
    axes[2].set_title('单根K线价格范围对比', fontsize=14, fontweight='bold')
    axes[2].set_ylabel('价格范围 (USDT)')
    axes[2].legend()
    axes[2].grid(True, alpha=0.3)
    
    # 4. 跳空幅度对比
    original_gaps = [0]  # 第一根K线没有跳空
    optimized_gaps = [0]
    
    for i in range(1, len(df_original)):
        orig_gap = abs(df_original['open'].iloc[i] - df_original['close'].iloc[i-1]) / df_original['close'].iloc[i-1]
        opt_gap = abs(df_optimized['open'].iloc[i] - df_optimized['close'].iloc[i-1]) / df_optimized['close'].iloc[i-1]
        original_gaps.append(orig_gap)
        optimized_gaps.append(opt_gap)
    
    axes[3].plot(df_original['datetime'], original_gaps, 
                label='修复前跳空幅度', color='red', alpha=0.7)
    axes[3].plot(df_optimized['datetime'], optimized_gaps, 
                label='修复后跳空幅度', color='blue')
    axes[3].axhline(y=0.03, color='orange', linestyle='--', alpha=0.8, label='3%限制线')
    axes[3].set_title('开盘跳空幅度对比', fontsize=14, fontweight='bold')
    axes[3].set_ylabel('跳空幅度')
    axes[3].set_xlabel('时间')
    axes[3].legend()
    axes[3].grid(True, alpha=0.3)
    axes[3].set_ylim(0, min(max(original_gaps), 0.15))  # 限制y轴范围
    
    # 设置x轴格式
    for ax in axes:
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
        ax.xaxis.set_major_locator(mdates.HourLocator(interval=2))
    
    plt.setp(axes[-1].xaxis.get_majorticklabels(), rotation=45)
    plt.tight_layout()
    
    # 保存图表
    filename = f'bybusdt_before_after_comparison_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"修复前后对比图已保存为: {filename}")
    
    plt.show()
    return filename

def main():
    """主函数"""
    try:
        print("="*80)
        print("BYBUSDT K线数据修复前后对比分析")
        print("="*80)
        
        # 1. 加载优化后数据
        print("\n正在读取优化后数据...")
        df_optimized = pd.read_csv('bybusdt_1min_kline_2025-07-03.csv')
        print(f"优化后K线数据条数: {len(df_optimized)}")
        
        # 2. 模拟原始数据
        df_original = simulate_original_data(df_optimized)
        
        # 3. 计算统计信息
        print("\n正在计算统计信息...")
        original_stats = calculate_statistics(df_original, "修复前")
        optimized_stats = calculate_statistics(df_optimized, "修复后")
        
        # 4. 打印对比报告
        print_comparison_report(original_stats, optimized_stats)
        
        # 5. 生成对比图表
        chart_file = plot_comparison_chart(df_original, df_optimized)
        
        # 6. 保存模拟的原始数据
        original_file = 'bybusdt_1min_kline_simulated_original_2025-07-03.csv'
        df_original.to_csv(original_file, index=False)
        print(f"\n模拟原始数据已保存为: {original_file}")
        
        print(f"\n=== 文件生成完成 ===")
        print(f"模拟原始数据: {original_file}")
        print(f"优化后数据: bybusdt_1min_kline_2025-07-03.csv")
        print(f"对比图表: {chart_file}")
        
        print(f"\n🎉 修复前后对比分析完成！")
        print(f"\n注意：原始数据是基于优化后数据模拟生成的，用于展示优化效果")
        
    except FileNotFoundError as e:
        print(f"错误: 文件未找到 - {e}")
        print("请确保文件 bybusdt_1min_kline_2025-07-03.csv 存在")
    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
