# BYB做市盈亏统计分析系统

## 概述

BYB做市盈亏统计分析系统是一个全面的交易盈亏分析工具，用于统计和分析BYB做市策略的盈亏情况，包括资产变化、交易盈亏、库存成本等关键指标。

## 功能特性

- ✅ **资产盈亏分析**: 计算BYB和USDT资产的变化和总盈亏
- ✅ **交易盈亏统计**: 基于历史成交记录计算详细的交易盈亏
- ✅ **库存成本分析**: 分析库存变化对盈亏的影响
- ✅ **手续费计算**: 统计交易手续费成本
- ✅ **时间序列分析**: 支持不同时间段的盈亏趋势分析
- ✅ **可视化图表**: 生成盈亏趋势、库存变化等图表
- ✅ **多格式输出**: 支持控制台显示、CSV导出、图表保存
- ✅ **Lark通知**: 可选择发送分析结果到飞书群

## 文件结构

```
byb_mm/
├── byb_pnl_analyzer.py          # 主分析脚本
├── test_pnl_analyzer.py         # 功能测试脚本
├── PNL_ANALYZER_README.md       # 使用说明文档
├── trade_history.csv            # 交易历史数据（自动生成）
├── inventory_history.json       # 库存历史数据（自动生成）
├── byb_pnl_report_*.csv         # 盈亏分析报告（运行后生成）
├── byb_pnl_analysis_*.png       # 可视化图表（可选生成）
└── byb_pnl_analyzer.log         # 分析日志（运行后生成）
```

## 依赖要求

### Python包依赖
```bash
pip install pandas numpy matplotlib seaborn aiohttp backoff pytz
```

### 数据文件要求
1. **trade_history.csv** - 交易历史记录文件
2. **inventory_history.json** - 库存历史记录文件
3. **con_pri.py** - API密钥配置文件

## 使用方法

### 1. 快速开始

```bash
# 进入项目目录
cd /path/to/byb_mm

# 运行功能测试
python3 test_pnl_analyzer.py

# 运行完整分析
python3 byb_pnl_analyzer.py

# 快速资产分析
python3 byb_pnl_analyzer.py --quick
```

### 2. 命令行参数

```bash
# 完整分析并发送到Lark
python3 byb_pnl_analyzer.py --lark

# 生成可视化图表
python3 byb_pnl_analyzer.py --charts

# 同时生成图表和发送Lark
python3 byb_pnl_analyzer.py --lark --charts

# 指定时间范围分析
python3 byb_pnl_analyzer.py --start-date 2024-01-01 --end-date 2024-12-31

# 快速资产分析并发送Lark
python3 byb_pnl_analyzer.py --quick --lark
```

### 3. 参数说明

| 参数 | 说明 |
|------|------|
| `--lark` | 发送分析报告到Lark（飞书） |
| `--charts` | 生成可视化图表 |
| `--start-date` | 分析开始日期 (YYYY-MM-DD) |
| `--end-date` | 分析结束日期 (YYYY-MM-DD) |
| `--quick` | 快速分析模式（仅当前资产） |

## 分析内容

### 1. 资产分析
- **BYB资产变化**: 当前BYB资产与初始资产的差值
- **USDT资产变化**: 当前USDT资产与初始资产的差值
- **总盈亏计算**: 以USDT计价的总盈亏（包含BYB价值变化）
- **当前价格**: 实时BYB/USDT价格

### 2. 交易分析
- **交易统计**: 总交易次数、买入/卖出次数
- **交易量统计**: 总交易量、买入/卖出量
- **价格分析**: 平均买入价、平均卖出价
- **手续费计算**: 基于交易额计算的手续费
- **净盈亏**: 扣除手续费后的净盈亏

### 3. 库存分析
- **库存变化**: 库存的增减情况
- **库存统计**: 最大/最小/平均库存
- **库存波动**: 库存的标准差
- **变化频率**: 库存变化的次数

### 4. 时间序列分析
- **盈亏趋势**: 累计盈亏随时间的变化
- **库存趋势**: 库存随时间的变化
- **交易分布**: 不同时间段的交易分布
- **价格分布**: 交易价格的分布情况

## 输出格式

### 1. 控制台报告
```
================================================================================
                              BYB做市策略盈亏分析报告                              
================================================================================
报告时间: 2024-06-30 15:30:00

📊 资产分析
----------------------------------------
BYB资产变化: 1,000,000.00 (16,000,000.00 - 15,000,000.00)
USDT资产变化: -50,000.00
当前BYB价格: 0.120000 USDT
BYB价值变化: 120,000.00 USDT
总盈亏(USDT): 70,000.00

📈 交易分析
----------------------------------------
交易期间: 2024-05-07 08:25 ~ 2024-06-30 15:30
总交易次数: 3,113 (买入: 1,556, 卖出: 1,557)
总交易量: 15,000,000.00 BYB
总交易额: 1,800,000.00 USDT
平均买入价: 0.119500 USDT
平均卖出价: 0.120500 USDT
交易手续费: 1,800.00 USDT
交易盈亏: 75,000.00 USDT
净盈亏: 73,200.00 USDT

📦 库存分析
----------------------------------------
库存期间: 2024-05-14 15:06 ~ 2024-06-30 15:30
库存变化: 1,000,000.00 BYB
最大库存: 250,086.32 BYB
最小库存: 69,067.20 BYB
平均库存: 120,000.00 BYB
库存波动: 45,000.00 BYB
库存变化次数: 1,500
```

### 2. CSV报告
包含所有分析指标的详细数据，便于进一步分析和存档。

### 3. 可视化图表
- **盈亏趋势图**: 显示累计盈亏随时间的变化
- **库存变化图**: 显示库存随时间的变化
- **价格分布图**: 显示买入和卖出价格的分布
- **交易量分布图**: 显示不同时间段的交易量

### 4. Lark通知
发送简化的分析摘要到飞书群，包含关键指标。

## 配置说明

### 1. 初始资产配置
在 `byb_pnl_analyzer.py` 中修改以下常量：
```python
INITIAL_BYB_INVENTORY = 15000000  # 初始BYB库存
INITIAL_USDT_INVENTORY = 1080657610806576.44674820  # 初始USDT库存
TRADING_FEE_RATE = 0.001  # 交易手续费率（0.1%）
```

### 2. Lark Webhook
系统使用与其他byb_mm脚本相同的Lark webhook地址。

## 故障排除

### 常见问题

1. **无法获取资产信息**
   - 检查API密钥配置
   - 检查网络连接
   - 确认API权限

2. **交易历史文件不存在**
   - 确认 `trade_history.csv` 文件存在
   - 检查文件格式和编码
   - 运行做市策略生成交易记录

3. **库存历史文件不存在**
   - 确认 `inventory_history.json` 文件存在
   - 检查JSON格式
   - 运行做市策略生成库存记录

4. **图表生成失败**
   - 安装matplotlib和seaborn
   - 检查中文字体配置
   - 确认有足够的数据

### 调试方法

```bash
# 运行功能测试
python3 test_pnl_analyzer.py

# 查看详细日志
tail -f byb_pnl_analyzer.log

# 检查数据文件
head -10 trade_history.csv
head -10 inventory_history.json
```

## 最佳实践

1. **定期运行**: 建议每日或每周运行一次完整分析
2. **数据备份**: 定期备份交易历史和库存历史文件
3. **参数调整**: 根据实际情况调整初始资产和手续费率
4. **监控预警**: 结合Lark通知实现盈亏监控预警

## 扩展功能

### 计划中的功能
- [ ] 数据库存储支持
- [ ] 更多可视化图表类型
- [ ] 风险指标分析
- [ ] 策略效果评估
- [ ] 自动化定时分析

### 自定义扩展
可以通过修改 `BYBPnLAnalyzer` 类来添加自定义分析功能。

## 联系支持

如有问题或建议，请联系开发团队。
