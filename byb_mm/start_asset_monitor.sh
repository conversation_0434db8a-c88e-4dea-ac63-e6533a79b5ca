#!/bin/bash

# BYB资产监控启动脚本
# 用于启动和管理资产监控进程

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PYTHON_SCRIPT="$SCRIPT_DIR/byb_asset_monitor.py"
PID_FILE="$SCRIPT_DIR/asset_monitor.pid"
LOG_FILE="$SCRIPT_DIR/byb_asset_monitor.log"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查进程是否运行
is_running() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0
        else
            rm -f "$PID_FILE"
            return 1
        fi
    fi
    return 1
}

# 启动监控
start_monitor() {
    if is_running; then
        print_message $YELLOW "资产监控已经在运行中 (PID: $(cat $PID_FILE))"
        return 1
    fi
    
    print_message $BLUE "启动BYB资产监控..."
    
    # 检查Python脚本是否存在
    if [ ! -f "$PYTHON_SCRIPT" ]; then
        print_message $RED "错误: 找不到监控脚本 $PYTHON_SCRIPT"
        return 1
    fi
    
    # 启动监控进程
    nohup python3 "$PYTHON_SCRIPT" > "$LOG_FILE" 2>&1 &
    local pid=$!
    echo $pid > "$PID_FILE"
    
    # 等待一下检查进程是否成功启动
    sleep 2
    if ps -p "$pid" > /dev/null 2>&1; then
        print_message $GREEN "✅ 资产监控启动成功 (PID: $pid)"
        print_message $BLUE "日志文件: $LOG_FILE"
        return 0
    else
        print_message $RED "❌ 资产监控启动失败"
        rm -f "$PID_FILE"
        return 1
    fi
}

# 停止监控
stop_monitor() {
    if ! is_running; then
        print_message $YELLOW "资产监控未运行"
        return 1
    fi
    
    local pid=$(cat "$PID_FILE")
    print_message $BLUE "停止BYB资产监控 (PID: $pid)..."
    
    kill "$pid"
    
    # 等待进程结束
    local count=0
    while ps -p "$pid" > /dev/null 2>&1 && [ $count -lt 10 ]; do
        sleep 1
        count=$((count + 1))
    done
    
    if ps -p "$pid" > /dev/null 2>&1; then
        print_message $YELLOW "进程未正常结束，强制终止..."
        kill -9 "$pid"
    fi
    
    rm -f "$PID_FILE"
    print_message $GREEN "✅ 资产监控已停止"
}

# 重启监控
restart_monitor() {
    print_message $BLUE "重启BYB资产监控..."
    stop_monitor
    sleep 2
    start_monitor
}

# 查看状态
status_monitor() {
    if is_running; then
        local pid=$(cat "$PID_FILE")
        print_message $GREEN "✅ 资产监控正在运行 (PID: $pid)"
        
        # 显示进程信息
        echo ""
        print_message $BLUE "进程信息:"
        ps -p "$pid" -o pid,ppid,cmd,etime,pcpu,pmem
        
        # 显示最近的日志
        if [ -f "$LOG_FILE" ]; then
            echo ""
            print_message $BLUE "最近的日志 (最后10行):"
            tail -n 10 "$LOG_FILE"
        fi
    else
        print_message $RED "❌ 资产监控未运行"
    fi
}

# 查看日志
view_logs() {
    if [ -f "$LOG_FILE" ]; then
        print_message $BLUE "查看资产监控日志:"
        echo "按 Ctrl+C 退出日志查看"
        echo ""
        tail -f "$LOG_FILE"
    else
        print_message $YELLOW "日志文件不存在: $LOG_FILE"
    fi
}

# 运行测试
run_test() {
    local test_script="$SCRIPT_DIR/test_asset_monitor.py"
    if [ -f "$test_script" ]; then
        print_message $BLUE "运行资产监控测试..."
        python3 "$test_script"
    else
        print_message $RED "错误: 找不到测试脚本 $test_script"
    fi
}

# 显示帮助信息
show_help() {
    echo "BYB资产监控管理脚本"
    echo ""
    echo "用法: $0 {start|stop|restart|status|logs|test|help}"
    echo ""
    echo "命令说明:"
    echo "  start   - 启动资产监控"
    echo "  stop    - 停止资产监控"
    echo "  restart - 重启资产监控"
    echo "  status  - 查看运行状态"
    echo "  logs    - 查看实时日志"
    echo "  test    - 运行功能测试"
    echo "  help    - 显示此帮助信息"
    echo ""
    echo "配置信息:"
    echo "  监控脚本: $PYTHON_SCRIPT"
    echo "  PID文件:  $PID_FILE"
    echo "  日志文件: $LOG_FILE"
}

# 主逻辑
case "$1" in
    start)
        start_monitor
        ;;
    stop)
        stop_monitor
        ;;
    restart)
        restart_monitor
        ;;
    status)
        status_monitor
        ;;
    logs)
        view_logs
        ;;
    test)
        run_test
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_message $RED "错误: 未知命令 '$1'"
        echo ""
        show_help
        exit 1
        ;;
esac
