#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BYB智能回购系统
功能：监控数据库参数变化，自动执行下单算法，支持参数更新和余额计算
作者：AI Assistant
创建时间：2025-07-07
"""

import pandas as pd
import pymysql
import asyncio
import logging
import traceback
import json
import os
import sys
from datetime import datetime, timedelta
from typing import Dict, Optional
import pytz
import time
import backoff
import aiohttp

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.append(project_root)
sys.path.append(current_dir)

from byex.spot.trade import SpotTrade
from byex.spot.market import Spot
import con_pri
from byb_order_algorithm import BYBOrderAlgorithm

# 时区设置
beijing_tz = pytz.timezone("Asia/Shanghai")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('byb_buy_back_system.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

