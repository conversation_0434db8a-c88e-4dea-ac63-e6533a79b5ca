#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BYB下单算法启动脚本
功能：提供交互式界面启动和管理下单算法
作者：AI Assistant
创建时间：2025-07-07
"""

import asyncio
import sys
import os
import signal
import logging
from datetime import datetime
import pytz

# 添加项目路径
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.append(project_root)
sys.path.append(current_dir)

from byb_order_algorithm import BYBOrderAlgorithm

# 时区设置
beijing_tz = pytz.timezone("Asia/Shanghai")

# 全局变量
algorithm_instance = None
running = False


def signal_handler(signum, frame):
    """信号处理器，用于优雅退出"""
    global running, algorithm_instance
    print(f"\n收到退出信号 {signum}，正在停止算法...")
    running = False
    if algorithm_instance:
        algorithm_instance.stop_algorithm()
    sys.exit(0)


def print_banner():
    """打印程序横幅"""
    print("=" * 80)
    print("BYB智能下单算法".center(80))
    print("=" * 80)
    print("功能：从数据库获取参数，在指定时间范围内随机分布下单")
    print("特点：每次下单200-800 BYB，时间间隔1-60分钟随机")
    print("监控：实时进度通知，完整执行日志")
    print("=" * 80)


def print_menu():
    """打印主菜单"""
    print("\n📋 主菜单")
    print("-" * 40)
    print("1. 查看数据库参数")
    print("2. 运行测试套件")
    print("3. 启动下单算法")
    print("4. 查看算法状态")
    print("5. 停止算法")
    print("6. 查看帮助")
    print("0. 退出程序")
    print("-" * 40)


async def show_database_params():
    """显示数据库参数"""
    try:
        print("\n📊 数据库参数")
        print("-" * 40)
        
        algorithm = BYBOrderAlgorithm()
        params = algorithm.get_buy_back_params()
        
        print(f"参数ID: {params['id']}")
        print(f"执行天数: {params['days']} 天")
        print(f"总下单量: {params['total_amount']:,.2f} BYB")
        print(f"建议单次量: {params['each_amount']:,.2f} BYB")
        print(f"创建时间: {params['created_at']}")
        
        # 计算预估信息
        avg_order_size = (200 + 800) / 2  # 平均订单大小
        estimated_orders = int(params['total_amount'] / avg_order_size)
        avg_interval = (60 + 3600) / 2 / 60  # 平均间隔（分钟）
        estimated_duration = estimated_orders * avg_interval / 60  # 预估时长（小时）
        
        print(f"\n📈 预估信息:")
        print(f"预估订单数: ~{estimated_orders} 个")
        print(f"平均订单大小: {avg_order_size:.0f} BYB")
        print(f"平均间隔: {avg_interval:.1f} 分钟")
        print(f"预估执行时长: {estimated_duration:.1f} 小时")
        
    except Exception as e:
        print(f"❌ 获取数据库参数失败: {str(e)}")


async def run_test_suite():
    """运行测试套件"""
    print("\n🧪 运行测试套件")
    print("-" * 40)
    
    try:
        from test_order_algorithm import run_all_tests
        await run_all_tests()
    except Exception as e:
        print(f"❌ 测试套件运行失败: {str(e)}")


async def start_algorithm():
    """启动下单算法"""
    global algorithm_instance, running
    
    if running:
        print("⚠️  算法已在运行中")
        return
    
    print("\n🚀 启动下单算法")
    print("-" * 40)
    
    try:
        # 确认启动
        confirm = input("确认启动下单算法？这将执行真实的买单操作 (y/N): ").strip().lower()
        if confirm != 'y':
            print("❌ 用户取消启动")
            return
        
        # 创建算法实例
        algorithm_instance = BYBOrderAlgorithm()
        running = True
        
        print("✅ 算法启动中...")
        print("💡 提示：按 Ctrl+C 可以安全停止算法")
        
        # 运行算法
        await algorithm_instance.run_algorithm()
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断算法执行")
    except Exception as e:
        print(f"❌ 算法执行失败: {str(e)}")
    finally:
        running = False
        algorithm_instance = None


def show_algorithm_status():
    """显示算法状态"""
    global algorithm_instance, running
    
    print("\n📊 算法状态")
    print("-" * 40)
    
    if not algorithm_instance:
        print("算法未启动")
        return
    
    try:
        status = algorithm_instance.get_status()
        
        print(f"运行状态: {'🟢 运行中' if status['is_running'] else '🔴 已停止'}")
        print(f"开始时间: {status['start_time'] or 'N/A'}")
        print(f"总订单数: {status['total_orders']}")
        print(f"已执行订单: {status['orders_executed']}")
        print(f"已执行金额: {status['total_executed']:,.2f} BYB")
        print(f"执行进度: {status['progress']:.1f}%")
        
        if status['total_orders'] > 0:
            remaining = status['total_orders'] - status['orders_executed']
            print(f"剩余订单: {remaining}")
        
    except Exception as e:
        print(f"❌ 获取状态失败: {str(e)}")


def stop_algorithm():
    """停止算法"""
    global algorithm_instance, running
    
    print("\n🛑 停止算法")
    print("-" * 40)
    
    if not running or not algorithm_instance:
        print("算法未在运行")
        return
    
    try:
        algorithm_instance.stop_algorithm()
        running = False
        print("✅ 停止信号已发送，算法将在当前订单完成后停止")
    except Exception as e:
        print(f"❌ 停止算法失败: {str(e)}")


def show_help():
    """显示帮助信息"""
    print("\n📖 帮助信息")
    print("-" * 40)
    print("1. 查看数据库参数：显示当前的交易参数设置")
    print("2. 运行测试套件：测试各个功能模块是否正常")
    print("3. 启动下单算法：开始执行真实的买单操作")
    print("4. 查看算法状态：显示当前执行进度和状态")
    print("5. 停止算法：安全停止正在运行的算法")
    print("6. 查看帮助：显示本帮助信息")
    print("\n💡 使用提示：")
    print("- 首次使用建议先运行测试套件")
    print("- 启动前请确认数据库参数正确")
    print("- 算法运行期间会发送Lark通知")
    print("- 可以随时查看执行状态和进度")
    print("- 使用Ctrl+C可以安全退出程序")


async def main_menu():
    """主菜单循环"""
    global running
    
    print_banner()
    
    while True:
        print_menu()
        
        try:
            choice = input("\n请选择操作 (0-6): ").strip()
            
            if choice == '0':
                print("👋 退出程序")
                if running and algorithm_instance:
                    algorithm_instance.stop_algorithm()
                break
            elif choice == '1':
                await show_database_params()
            elif choice == '2':
                await run_test_suite()
            elif choice == '3':
                await start_algorithm()
            elif choice == '4':
                show_algorithm_status()
            elif choice == '5':
                stop_algorithm()
            elif choice == '6':
                show_help()
            else:
                print("❌ 无效选择，请输入0-6之间的数字")
                
        except KeyboardInterrupt:
            print("\n👋 用户退出程序")
            if running and algorithm_instance:
                algorithm_instance.stop_algorithm()
            break
        except Exception as e:
            print(f"❌ 操作失败: {str(e)}")
        
        # 等待用户按键继续
        if choice in ['1', '4', '5', '6']:
            input("\n按回车键继续...")


if __name__ == "__main__":
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    try:
        # 运行主菜单
        asyncio.run(main_menu())
    except Exception as e:
        print(f"程序异常退出: {str(e)}")
        sys.exit(1)
