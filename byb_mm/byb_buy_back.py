import pandas as pd
import pymysql
import asyncio
import logging
import traceback
import json
import os
import sys
from datetime import datetime, timedelta
from typing import Dict, Optional
import pytz
import time
import backoff
import aiohttp

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.append(project_root)
sys.path.append(current_dir)

from byex.spot.trade import SpotTrade
from byex.spot.market import Spot
import con_pri
from byb_order_algorithm import BYBOrderAlgorithm

# 时区设置
beijing_tz = pytz.timezone("Asia/Shanghai")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('byb_buy_back_system.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# 数据库连接信息
DB_CONFIG = {
    "host": "127.0.0.1",
    "port": 3306,
    "user": "weili",
    "password": "12345678",
    "database": "buy_back"
}

# 监控配置
MONITOR_CONFIG = {
    "check_interval": 30,  # 参数检查间隔（秒）
    "symbol": "bybusdt",   # 交易对
    "base_currency": "byb", # 基础货币
    "quote_currency": "usdt" # 计价货币
}


@backoff.on_exception(backoff.expo, Exception, max_tries=3)
async def send_lark(msg, level='info'):
    """发送消息到Lark（飞书）"""
    headers = {'Content-Type': 'application/json'}
    payload_message = {
        "msg_type": "text",
        "content": {
            "text": f"【{level.upper()}】BYB回购系统\n{str(msg)}"
        }
    }
    webhook = 'https://open.larksuite.com/open-apis/bot/v2/hook/230d3321-92cb-44b4-8dcc-a77b2e2c4c2a'
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(webhook, data=json.dumps(payload_message), headers=headers, timeout=10) as response:
                res = await response.json()
                statuscode = res.get('StatusCode', 404)
                if statuscode != 0 or res["StatusMessage"] != 'success':
                    logging.error(f"Lark webhook response error: {res}")
    except Exception as e:
        logging.error(f"Lark notification error for message '{msg}': {str(e)}")


def schedule_lark_message(msg, level='info'):
    """在当前事件循环中调度Lark消息发送任务，不阻塞主程序"""
    try:
        loop = asyncio.get_event_loop()
        if loop.is_running():
            task = loop.create_task(send_lark(msg, level))
            task.add_done_callback(lambda t: None if not t.exception() else
                                 logging.error(f"Lark消息发送任务失败: {t.exception()}"))
        else:
            asyncio.run(send_lark(msg, level))
    except Exception as e:
        logging.error(f"调度Lark消息失败: {str(e)}")


class BYBBuyBackSystem:
    """BYB智能回购系统"""

    def __init__(self):
        """初始化回购系统"""
        self.db_config = DB_CONFIG
        self.monitor_config = MONITOR_CONFIG

        # 初始化交易客户端
        self.spot_client = SpotTrade(con_pri.api_key, con_pri.api_secret)
        self.spot_market = Spot()
        SpotTrade.BASE_URL = "https://openapi.100exdemo.com"
        Spot.BASE_URL = "https://openapi.100exdemo.com"

        # 系统状态
        self.is_running = False
        self.current_algorithm = None
        self.last_params = None
        self.execution_history = []
        self.total_spent_usdt = 0.0

        # 状态文件路径
        self.state_file = "byb_buy_back_state.json"

        # 加载历史状态
        self.load_state()

        logging.info("BYB回购系统初始化完成")

    def get_buy_back_params(self) -> Dict:
        """从数据库获取回购参数"""
        try:
            conn = pymysql.connect(**self.db_config)
            with conn.cursor() as cursor:
                cursor.execute("SELECT * FROM buy_back_params ORDER BY created_at DESC LIMIT 1;")
                result = cursor.fetchone()
                if result:
                    params = {
                        'id': result[0],
                        'days': float(result[1]),
                        'total_amount': float(result[2]),
                        'each_amount': float(result[3]),
                        'created_at': result[4]
                    }
                    return params
                else:
                    raise ValueError("数据库中没有找到回购参数")
        except Exception as e:
            logging.error(f"获取数据库参数失败: {str(e)}")
            raise
        finally:
            if 'conn' in locals():
                conn.close()

    def get_account_balance(self) -> Dict:
        """获取账户余额"""
        try:
            account_info = self.spot_client.account()
            if not account_info or 'coin_list' not in account_info:
                raise ValueError("获取账户信息失败")

            coin_list = account_info['coin_list']
            balances = {}

            for coin_info in coin_list:
                coin = coin_info['coin'].lower()
                normal = float(coin_info.get('normal', 0))
                locked = float(coin_info.get('locked', 0))
                total = normal + locked
                balances[coin] = {
                    'normal': normal,
                    'locked': locked,
                    'total': total
                }

            logging.info(f"获取账户余额成功: BYB={balances.get('byb', {}).get('total', 0):.2f}, USDT={balances.get('usdt', {}).get('total', 0):.2f}")
            return balances

        except Exception as e:
            logging.error(f"获取账户余额失败: {str(e)}")
            raise

    def load_state(self):
        """加载历史状态"""
        try:
            if os.path.exists(self.state_file):
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    state = json.load(f)
                    self.total_spent_usdt = state.get('total_spent_usdt', 0.0)
                    self.execution_history = state.get('execution_history', [])
                    logging.info(f"加载历史状态: 已花费USDT={self.total_spent_usdt:.2f}, 执行记录={len(self.execution_history)}条")
            else:
                logging.info("未找到历史状态文件，从零开始")
        except Exception as e:
            logging.error(f"加载历史状态失败: {str(e)}")
            self.total_spent_usdt = 0.0
            self.execution_history = []

    def save_state(self):
        """保存当前状态"""
        try:
            state = {
                'total_spent_usdt': self.total_spent_usdt,
                'execution_history': self.execution_history,
                'last_update': datetime.now(beijing_tz).isoformat()
            }
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(state, f, ensure_ascii=False, indent=2)
            logging.debug(f"状态已保存: 已花费USDT={self.total_spent_usdt:.2f}")
        except Exception as e:
            logging.error(f"保存状态失败: {str(e)}")

    def params_changed(self, new_params: Dict) -> bool:
        """检查参数是否发生变化"""
        if not self.last_params:
            return True

        # 比较关键参数
        key_fields = ['days', 'total_amount', 'each_amount']
        for field in key_fields:
            if self.last_params.get(field) != new_params.get(field):
                logging.info(f"参数变化检测: {field} 从 {self.last_params.get(field)} 变为 {new_params.get(field)}")
                return True

        return False

    def calculate_remaining_amount(self, total_amount: float) -> float:
        """计算剩余需要购买的金额"""
        remaining = total_amount - self.total_spent_usdt
        logging.info(f"计算剩余金额: 总金额={total_amount:.2f}, 已花费={self.total_spent_usdt:.2f}, 剩余={remaining:.2f}")
        return max(0, remaining)

    async def update_spent_amount(self):
        """更新已花费金额（通过查询交易记录）"""
        try:
            # 获取最近的交易记录
            # 这里可以通过API查询交易历史来计算实际花费
            # 暂时使用简化逻辑，实际项目中需要根据API接口实现

            # 示例：从执行历史中计算
            total_spent = 0.0
            for record in self.execution_history:
                if record.get('status') == 'completed' and 'spent_usdt' in record:
                    total_spent += record['spent_usdt']

            self.total_spent_usdt = total_spent
            logging.info(f"更新已花费金额: {self.total_spent_usdt:.2f} USDT")

        except Exception as e:
            logging.error(f"更新已花费金额失败: {str(e)}")

    async def stop_current_algorithm(self):
        """停止当前运行的算法"""
        if self.current_algorithm and self.current_algorithm.is_running:
            logging.info("停止当前运行的下单算法")
            self.current_algorithm.stop_algorithm()

            # 等待算法停止
            max_wait = 30  # 最多等待30秒
            wait_count = 0
            while self.current_algorithm.is_running and wait_count < max_wait:
                await asyncio.sleep(1)
                wait_count += 1

            if self.current_algorithm.is_running:
                logging.warning("算法未能在预期时间内停止")
            else:
                logging.info("算法已成功停止")

    async def start_new_algorithm(self, params: Dict, remaining_amount: float):
        """启动新的下单算法"""
        try:
            each_amount = params.get('each_amount', 100.0)  # 默认100 BYB
            logging.info(f"启动新的下单算法: 剩余金额={remaining_amount:.2f} BYB, 时间={params['days']} 天, 单次最大={each_amount:.2f} BYB")

            # 创建新的算法实例
            self.current_algorithm = BYBOrderAlgorithm()

            # 修改算法参数以使用剩余金额和each_amount
            original_get_params = self.current_algorithm.get_buy_back_params

            def modified_get_params():
                modified_params = params.copy()
                modified_params['total_amount'] = remaining_amount
                # 保持原有的each_amount参数
                return modified_params

            self.current_algorithm.get_buy_back_params = modified_get_params

            # 启动算法
            await self.current_algorithm.run_algorithm()

            # 记录执行结果
            execution_record = {
                'start_time': datetime.now(beijing_tz).isoformat(),
                'params': params,
                'remaining_amount': remaining_amount,
                'status': 'completed' if not self.current_algorithm.is_running else 'interrupted',
                'orders_executed': self.current_algorithm.orders_executed,
                'total_executed': self.current_algorithm.total_executed
            }

            self.execution_history.append(execution_record)
            self.save_state()

            logging.info(f"算法执行完成: 执行订单={self.current_algorithm.orders_executed}, 执行金额={self.current_algorithm.total_executed:.2f}")

        except Exception as e:
            logging.error(f"启动新算法失败: {str(e)}\n{traceback.format_exc()}")
            schedule_lark_message(f"启动下单算法失败: {str(e)}", level='error')

    async def monitor_and_execute(self):
        """主监控循环：监控参数变化并执行下单算法"""
        try:
            self.is_running = True

            # 发送启动通知
            start_msg = (
                f"🚀 BYB回购系统启动\n"
                f"监控间隔: {self.monitor_config['check_interval']} 秒\n"
                f"交易对: {self.monitor_config['symbol']}\n"
                f"历史花费: {self.total_spent_usdt:.2f} USDT\n"
                f"启动时间: {datetime.now(beijing_tz).strftime('%Y-%m-%d %H:%M:%S')}"
            )
            schedule_lark_message(start_msg, level='info')

            while self.is_running:
                try:
                    # 获取当前数据库参数
                    current_params = self.get_buy_back_params()

                    # 检查参数是否发生变化
                    if self.params_changed(current_params):
                        logging.info("检测到参数变化，开始处理...")

                        # 停止当前算法（如果正在运行）
                        await self.stop_current_algorithm()

                        # 更新已花费金额
                        await self.update_spent_amount()

                        # 获取账户余额
                        try:
                            balances = self.get_account_balance()
                            usdt_balance = balances.get('usdt', {}).get('total', 0)
                            byb_balance = balances.get('byb', {}).get('total', 0)
                        except Exception as e:
                            logging.warning(f"获取余额失败，使用默认值: {str(e)}")
                            usdt_balance = 0
                            byb_balance = 0

                        # 计算剩余需要购买的金额
                        remaining_amount = self.calculate_remaining_amount(current_params['total_amount'])

                        if remaining_amount > 0:
                            # 检查USDT余额是否足够
                            if usdt_balance < remaining_amount * 0.1:  # 假设BYB价格约0.1 USDT，这里需要根据实际价格调整
                                warning_msg = (
                                    f"⚠️ USDT余额不足\n"
                                    f"当前USDT余额: {usdt_balance:.2f}\n"
                                    f"剩余需购买: {remaining_amount:.2f} BYB\n"
                                    f"请及时充值USDT"
                                )
                                schedule_lark_message(warning_msg, level='warning')
                                logging.warning("USDT余额不足，跳过本次执行")
                            else:
                                # 发送参数变化通知
                                change_msg = (
                                    f"📊 参数变化检测\n"
                                    f"新参数: {current_params['days']} 天, {current_params['total_amount']:.2f} BYB\n"
                                    f"已花费: {self.total_spent_usdt:.2f} USDT\n"
                                    f"剩余购买: {remaining_amount:.2f} BYB\n"
                                    f"USDT余额: {usdt_balance:.2f}\n"
                                    f"开始执行新的下单计划..."
                                )
                                schedule_lark_message(change_msg, level='info')

                                # 启动新的下单算法
                                await self.start_new_algorithm(current_params, remaining_amount)
                        else:
                            # 已完成所有购买
                            complete_msg = (
                                f"✅ 回购任务已完成\n"
                                f"目标金额: {current_params['total_amount']:.2f} BYB\n"
                                f"已花费: {self.total_spent_usdt:.2f} USDT\n"
                                f"无需继续购买"
                            )
                            schedule_lark_message(complete_msg, level='info')
                            logging.info("回购任务已完成，无需继续购买")

                        # 更新最后参数
                        self.last_params = current_params
                        self.save_state()

                    else:
                        logging.debug("参数未发生变化，继续监控...")

                    # 等待下次检查
                    await asyncio.sleep(self.monitor_config['check_interval'])

                except Exception as e:
                    logging.error(f"监控循环异常: {str(e)}\n{traceback.format_exc()}")
                    schedule_lark_message(f"监控循环异常: {str(e)}", level='error')
                    await asyncio.sleep(60)  # 异常时等待1分钟再继续

        except Exception as e:
            logging.error(f"监控系统异常: {str(e)}\n{traceback.format_exc()}")
            schedule_lark_message(f"监控系统异常: {str(e)}", level='error')
        finally:
            self.is_running = False
            logging.info("BYB回购系统已停止")

    def stop_system(self):
        """停止回购系统"""
        self.is_running = False
        if self.current_algorithm:
            self.current_algorithm.stop_algorithm()
        logging.info("回购系统停止信号已发送")

    def get_system_status(self) -> Dict:
        """获取系统状态"""
        algorithm_status = None
        if self.current_algorithm:
            algorithm_status = self.current_algorithm.get_status()

        return {
            'system_running': self.is_running,
            'total_spent_usdt': self.total_spent_usdt,
            'execution_history_count': len(self.execution_history),
            'last_params': self.last_params,
            'current_algorithm': algorithm_status
        }


# 原始的简单函数保持兼容性
def get_buy_back_params():
    """获取回购参数（兼容性函数）"""
    system = BYBBuyBackSystem()
    return system.get_buy_back_params()


async def main():
    """主程序入口"""
    try:
        # 创建回购系统实例
        buy_back_system = BYBBuyBackSystem()

        # 运行监控系统
        await buy_back_system.monitor_and_execute()

    except KeyboardInterrupt:
        logging.info("程序被用户中断")
        if 'buy_back_system' in locals():
            buy_back_system.stop_system()
    except Exception as e:
        logging.error(f"主程序异常: {str(e)}\n{traceback.format_exc()}")


if __name__ == '__main__':
    print("=" * 80)
    print("BYB智能回购系统".center(80))
    print("=" * 80)
    print("功能：监控数据库参数变化，自动执行下单算法")
    print("特点：支持参数更新、余额计算、动态调整")
    print("监控：实时参数监控、Lark通知、完整日志")
    print("=" * 80)

    # 运行主程序
    asyncio.run(main())

