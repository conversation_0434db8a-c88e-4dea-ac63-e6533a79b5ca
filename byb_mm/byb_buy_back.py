import pandas as pd
import pymysql

# 数据库连接信息
host = "127.0.0.1"  # 服务器地址
port = 3306  # MySQL 端口
user = "weili"  # 数据库用户名
password = "12345678"  # 数据库密码
database = "buy_back"  # 要连接


def get_buy_back_params():
    # 连接数据库
    conn = pymysql.connect(host=host, port=port, user=user, password=password, database=database)
    try:
        with conn.cursor() as cursor:
            # 执行 SQL 查询
            cursor.execute("SELECT * FROM buy_back_params;")
            # 获取查询结果
            result = cursor.fetchall()
            dataframe = pd.DataFrame(result, columns=['id', 'days', 'total_amount', 'each_amount', 'created_at'])
            dataframe[['days', 'total_amount', 'each_amount']] = dataframe[['days', 'total_amount', 'each_amount']].astype(float)
            return dataframe
    finally:
        conn.close()  # 关闭连接


if __name__ == '__main__':
    df_params = get_buy_back_params().iloc[-1]
    print(df_params)