#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BYB回购系统演示版本
功能：模拟数据库和API调用，演示系统完整功能
作者：AI Assistant
创建时间：2025-07-07
"""

import asyncio
import logging
import json
import os
import sys
from datetime import datetime, timedelta
from typing import Dict, Optional
import pytz
import random

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.append(project_root)
sys.path.append(current_dir)

# 时区设置
beijing_tz = pytz.timezone("Asia/Shanghai")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('demo_buy_back_system.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)


class MockBYBBuyBackSystem:
    """BYB回购系统演示版本（模拟数据库和API）"""
    
    def __init__(self):
        """初始化演示系统"""
        # 模拟数据库参数
        self.mock_params = {
            'id': 1,
            'days': 7.0,
            'total_amount': 50000.0,
            'each_amount': 500.0,
            'created_at': datetime.now(beijing_tz)
        }
        
        # 模拟账户余额
        self.mock_balance = {
            'byb': {'normal': 100000.0, 'locked': 0.0, 'total': 100000.0},
            'usdt': {'normal': 10000.0, 'locked': 0.0, 'total': 10000.0}
        }
        
        # 系统状态
        self.is_running = False
        self.last_params = None
        self.total_spent_usdt = 0.0
        self.execution_history = []
        
        # 状态文件
        self.state_file = "demo_buy_back_state.json"
        self.load_state()
        
        logging.info("演示版BYB回购系统初始化完成")
    
    def get_buy_back_params(self) -> Dict:
        """模拟获取数据库参数"""
        logging.info(f"获取模拟参数: {self.mock_params}")
        return self.mock_params.copy()
    
    def get_account_balance(self) -> Dict:
        """模拟获取账户余额"""
        logging.info(f"获取模拟余额: BYB={self.mock_balance['byb']['total']:.2f}, USDT={self.mock_balance['usdt']['total']:.2f}")
        return self.mock_balance.copy()
    
    def load_state(self):
        """加载历史状态"""
        try:
            if os.path.exists(self.state_file):
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    state = json.load(f)
                    self.total_spent_usdt = state.get('total_spent_usdt', 0.0)
                    self.execution_history = state.get('execution_history', [])
                    logging.info(f"加载演示状态: 已花费USDT={self.total_spent_usdt:.2f}, 执行记录={len(self.execution_history)}条")
            else:
                logging.info("未找到演示状态文件，从零开始")
        except Exception as e:
            logging.error(f"加载演示状态失败: {str(e)}")
            self.total_spent_usdt = 0.0
            self.execution_history = []
    
    def save_state(self):
        """保存当前状态"""
        try:
            state = {
                'total_spent_usdt': self.total_spent_usdt,
                'execution_history': self.execution_history,
                'last_update': datetime.now(beijing_tz).isoformat()
            }
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(state, f, ensure_ascii=False, indent=2)
            logging.debug(f"演示状态已保存: 已花费USDT={self.total_spent_usdt:.2f}")
        except Exception as e:
            logging.error(f"保存演示状态失败: {str(e)}")
    
    def params_changed(self, new_params: Dict) -> bool:
        """检查参数是否发生变化"""
        if not self.last_params:
            return True
        
        key_fields = ['days', 'total_amount', 'each_amount']
        for field in key_fields:
            if self.last_params.get(field) != new_params.get(field):
                logging.info(f"参数变化检测: {field} 从 {self.last_params.get(field)} 变为 {new_params.get(field)}")
                return True
        
        return False
    
    def calculate_remaining_amount(self, total_amount: float) -> float:
        """计算剩余需要购买的金额"""
        remaining = total_amount - self.total_spent_usdt
        logging.info(f"计算剩余金额: 总金额={total_amount:.2f}, 已花费={self.total_spent_usdt:.2f}, 剩余={remaining:.2f}")
        return max(0, remaining)
    
    async def simulate_order_execution(self, remaining_amount: float, days: float, each_amount: float = 500.0) -> Dict:
        """模拟订单执行"""
        logging.info(f"开始模拟执行: {remaining_amount:.2f} BYB, {days} 天, 单次最大={each_amount:.2f} BYB")

        # 模拟执行时间（实际会很长，这里缩短为几秒）
        execution_time = min(10, days * 0.1)  # 最多10秒

        # 模拟分批执行
        executed_orders = 0
        executed_amount = 0.0
        spent_usdt = 0.0

        batch_count = max(1, int(remaining_amount / each_amount))  # 根据each_amount计算批次
        
        for i in range(batch_count):
            await asyncio.sleep(execution_time / batch_count)

            # 模拟单批执行，使用each_amount作为最大单次量
            batch_amount = min(each_amount, remaining_amount - executed_amount)
            batch_price = random.uniform(0.08, 0.12)  # 模拟BYB价格
            batch_spent = batch_amount * batch_price

            executed_orders += 1
            executed_amount += batch_amount
            spent_usdt += batch_spent

            logging.info(f"模拟执行批次 {i+1}/{batch_count}: {batch_amount:.2f} BYB, 花费 {batch_spent:.2f} USDT")

            if executed_amount >= remaining_amount:
                break
        
        # 更新系统状态
        self.total_spent_usdt += spent_usdt
        self.mock_balance['usdt']['total'] -= spent_usdt
        self.mock_balance['byb']['total'] += executed_amount
        
        result = {
            'executed_orders': executed_orders,
            'executed_amount': executed_amount,
            'spent_usdt': spent_usdt,
            'status': 'completed'
        }
        
        logging.info(f"模拟执行完成: {result}")
        return result
    
    async def simulate_parameter_change(self):
        """模拟参数变化"""
        await asyncio.sleep(5)  # 5秒后模拟参数变化
        
        # 修改参数
        self.mock_params['total_amount'] += 20000  # 增加20000 BYB
        self.mock_params['days'] = 10.0  # 改为10天
        self.mock_params['id'] += 1
        self.mock_params['created_at'] = datetime.now(beijing_tz)
        
        logging.info(f"模拟参数变化: 新参数 {self.mock_params}")
    
    async def monitor_and_execute(self):
        """主监控循环演示"""
        try:
            self.is_running = True
            
            print(f"\n🚀 演示版BYB回购系统启动")
            print(f"初始参数: {self.mock_params['days']} 天, {self.mock_params['total_amount']:.2f} BYB")
            print(f"初始余额: BYB={self.mock_balance['byb']['total']:.2f}, USDT={self.mock_balance['usdt']['total']:.2f}")
            print(f"已花费: {self.total_spent_usdt:.2f} USDT")
            print("=" * 80)
            
            # 启动参数变化模拟任务
            asyncio.create_task(self.simulate_parameter_change())
            
            cycle_count = 0
            while self.is_running and cycle_count < 3:  # 最多运行3个周期
                cycle_count += 1
                print(f"\n📊 监控周期 {cycle_count}")
                
                try:
                    # 获取当前参数
                    current_params = self.get_buy_back_params()
                    
                    # 检查参数变化
                    if self.params_changed(current_params):
                        print(f"✅ 检测到参数变化")
                        
                        # 获取余额
                        balances = self.get_account_balance()
                        usdt_balance = balances['usdt']['total']
                        
                        # 计算剩余金额
                        remaining_amount = self.calculate_remaining_amount(current_params['total_amount'])
                        
                        if remaining_amount > 0:
                            estimated_cost = remaining_amount * 0.1  # 估算成本
                            
                            if usdt_balance >= estimated_cost:
                                each_amount = current_params.get('each_amount', 500.0)
                                print(f"💰 开始执行: 剩余 {remaining_amount:.2f} BYB, 预估成本 {estimated_cost:.2f} USDT, 单次最大 {each_amount:.2f} BYB")

                                # 模拟执行
                                result = await self.simulate_order_execution(remaining_amount, current_params['days'], each_amount)
                                
                                # 记录执行结果
                                params_for_record = current_params.copy()
                                if 'created_at' in params_for_record:
                                    params_for_record['created_at'] = params_for_record['created_at'].isoformat()

                                execution_record = {
                                    'cycle': cycle_count,
                                    'start_time': datetime.now(beijing_tz).isoformat(),
                                    'params': params_for_record,
                                    'remaining_amount': remaining_amount,
                                    'result': result
                                }
                                self.execution_history.append(execution_record)
                                
                                print(f"✅ 执行完成: {result['executed_orders']} 订单, {result['executed_amount']:.2f} BYB, 花费 {result['spent_usdt']:.2f} USDT")
                                
                            else:
                                print(f"⚠️ USDT余额不足: 需要 {estimated_cost:.2f}, 当前 {usdt_balance:.2f}")
                        else:
                            print(f"✅ 回购任务已完成，无需继续购买")
                        
                        # 更新最后参数
                        self.last_params = current_params
                        self.save_state()
                        
                    else:
                        print(f"📝 参数未变化，继续监控...")
                    
                    # 显示当前状态
                    print(f"📈 当前状态: 已花费 {self.total_spent_usdt:.2f} USDT, 执行记录 {len(self.execution_history)} 条")
                    
                    await asyncio.sleep(3)  # 演示用，实际是30秒
                    
                except Exception as e:
                    logging.error(f"监控循环异常: {str(e)}")
                    await asyncio.sleep(1)
            
            print(f"\n🎉 演示完成")
            print(f"总执行周期: {cycle_count}")
            print(f"总花费: {self.total_spent_usdt:.2f} USDT")
            print(f"执行记录: {len(self.execution_history)} 条")
            
        except Exception as e:
            logging.error(f"演示系统异常: {str(e)}")
        finally:
            self.is_running = False
            self.save_state()
    
    def get_system_status(self) -> Dict:
        """获取系统状态"""
        return {
            'system_running': self.is_running,
            'total_spent_usdt': self.total_spent_usdt,
            'execution_history_count': len(self.execution_history),
            'last_params': self.last_params,
            'mock_balance': self.mock_balance
        }


async def main():
    """演示主程序"""
    try:
        print("=" * 80)
        print("BYB智能回购系统 - 演示版".center(80))
        print("=" * 80)
        print("功能演示：参数监控、余额管理、动态调整、状态持久化")
        print("注意：本演示使用模拟数据，不会执行真实交易")
        print("=" * 80)
        
        # 创建演示系统
        demo_system = MockBYBBuyBackSystem()
        
        # 运行演示
        await demo_system.monitor_and_execute()
        
        # 显示最终状态
        final_status = demo_system.get_system_status()
        print(f"\n📊 最终状态:")
        print(f"总花费: {final_status['total_spent_usdt']:.2f} USDT")
        print(f"执行记录: {final_status['execution_history_count']} 条")
        print(f"BYB余额: {final_status['mock_balance']['byb']['total']:.2f}")
        print(f"USDT余额: {final_status['mock_balance']['usdt']['total']:.2f}")
        
    except KeyboardInterrupt:
        print("\n👋 演示被用户中断")
    except Exception as e:
        logging.error(f"演示程序异常: {str(e)}")


if __name__ == '__main__':
    asyncio.run(main())
