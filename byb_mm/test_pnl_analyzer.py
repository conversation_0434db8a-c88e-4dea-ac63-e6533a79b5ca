#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BYB盈亏分析器测试脚本
用于测试盈亏分析功能
"""

import asyncio
import logging
import sys
import pandas as pd
from datetime import datetime
import pytz

# 添加项目路径
sys.path.append(r'/home/<USER>/byb_mm/')

# 导入必要的模块
from byex.spot.trade import SpotTrade
from byex.spot.market import Spot
import con_pri

# 设置北京时区
beijing_tz = pytz.timezone("Asia/Shanghai")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# 初始化API客户端
spot_client = SpotTrade(con_pri.api_key, con_pri.api_secret)
spot_market = Spot()


def test_api_connection():
    """测试API连接"""
    print("=" * 60)
    print("测试API连接")
    print("=" * 60)
    
    try:
        # 测试账户API
        account = spot_client.account()
        if account and 'coin_list' in account:
            print("✅ 账户API连接成功")
            coin_count = len(account['coin_list'])
            print(f"   账户币种数量: {coin_count}")
        else:
            print("❌ 账户API连接失败")
            return False
        
        # 测试市场API
        ticker = spot_market.get_ticker('bybusdt')
        if ticker and 'last' in ticker:
            print("✅ 市场API连接成功")
            print(f"   当前BYB价格: {ticker['last']}")
        else:
            print("❌ 市场API连接失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ API连接测试失败: {str(e)}")
        return False


def test_asset_calculation():
    """测试资产计算"""
    print("\n" + "=" * 60)
    print("测试资产计算")
    print("=" * 60)
    
    try:
        # 获取账户信息
        account = spot_client.account()
        if not account or 'coin_list' not in account:
            print("❌ 无法获取账户信息")
            return False
        
        df_account = pd.DataFrame(account['coin_list'])
        print(f"账户币种数量: {len(df_account)}")
        
        # 查找BYB和USDT资产
        for coin in ['byb', 'usdt']:
            coin_assets = df_account[df_account['coin'] == coin]
            if not coin_assets.empty:
                normal = float(coin_assets['normal'].iloc[0])
                locked = float(coin_assets['locked'].iloc[0])
                total = normal + locked
                print(f"{coin.upper()}资产:")
                print(f"   可用: {normal:,.2f}")
                print(f"   冻结: {locked:,.2f}")
                print(f"   总计: {total:,.2f}")
            else:
                print(f"{coin.upper()}资产: 未找到")
        
        # 获取BYB价格
        ticker = spot_market.get_ticker('bybusdt')
        if ticker and 'last' in ticker:
            price = float(ticker['last'])
            print(f"当前BYB价格: {price:.6f} USDT")
        
        return True
        
    except Exception as e:
        print(f"❌ 资产计算测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_data_loading():
    """测试数据加载"""
    print("\n" + "=" * 60)
    print("测试数据加载")
    print("=" * 60)
    
    import os
    
    # 测试交易历史文件
    trade_file = 'trade_history.csv'
    if os.path.exists(trade_file):
        try:
            df = pd.read_csv(trade_file, encoding='utf-8')
            print(f"✅ 交易历史文件加载成功")
            print(f"   记录数量: {len(df):,}")
            print(f"   列名: {list(df.columns)}")
            
            # 显示前几行数据
            if not df.empty:
                print(f"   时间范围: {df.iloc[0]['成交时间'] if '成交时间' in df.columns else 'N/A'} ~ {df.iloc[-1]['成交时间'] if '成交时间' in df.columns else 'N/A'}")
                
        except Exception as e:
            print(f"❌ 交易历史文件加载失败: {str(e)}")
    else:
        print(f"⚠️  交易历史文件不存在: {trade_file}")
    
    # 测试库存历史文件
    inventory_file = 'inventory_history.json'
    if os.path.exists(inventory_file):
        try:
            import json
            with open(inventory_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(f"✅ 库存历史文件加载成功")
            print(f"   记录数量: {len(data):,}")
            
            if data:
                first_record = data[0]
                last_record = data[-1]
                print(f"   时间范围: {first_record.get('datetime', 'N/A')} ~ {last_record.get('datetime', 'N/A')}")
                print(f"   库存范围: {first_record.get('inventory', 'N/A')} ~ {last_record.get('inventory', 'N/A')}")
                
        except Exception as e:
            print(f"❌ 库存历史文件加载失败: {str(e)}")
    else:
        print(f"⚠️  库存历史文件不存在: {inventory_file}")


def test_pnl_analyzer():
    """测试盈亏分析器"""
    print("\n" + "=" * 60)
    print("测试盈亏分析器")
    print("=" * 60)
    
    try:
        # 导入分析器
        from byb_pnl_analyzer import BYBPnLAnalyzer
        
        # 创建分析器实例
        analyzer = BYBPnLAnalyzer()
        print("✅ 盈亏分析器创建成功")
        
        # 测试获取当前资产
        print("\n测试获取当前资产...")
        assets = analyzer.get_current_assets()
        if assets:
            print("✅ 当前资产获取成功")
            for coin, data in assets.items():
                print(f"   {coin.upper()}: {data['total']:,.2f}")
        else:
            print("❌ 当前资产获取失败")
        
        # 测试加载交易历史
        print("\n测试加载交易历史...")
        trade_data = analyzer.load_trade_history()
        if trade_data is not None:
            print(f"✅ 交易历史加载成功: {len(trade_data)} 条记录")
        else:
            print("⚠️  交易历史加载失败或文件不存在")
        
        # 测试加载库存历史
        print("\n测试加载库存历史...")
        inventory_data = analyzer.load_inventory_history()
        if inventory_data is not None:
            print(f"✅ 库存历史加载成功: {len(inventory_data)} 条记录")
        else:
            print("⚠️  库存历史加载失败或文件不存在")
        
        # 测试资产盈亏计算
        print("\n测试资产盈亏计算...")
        asset_pnl = analyzer.calculate_asset_pnl()
        if asset_pnl:
            print("✅ 资产盈亏计算成功")
            print(f"   BYB变化: {asset_pnl['byb_change']:,.2f}")
            print(f"   USDT变化: {asset_pnl['usdt_change']:,.2f}")
            print(f"   总盈亏: {asset_pnl['total_pnl_usdt']:,.2f} USDT")
        else:
            print("❌ 资产盈亏计算失败")
        
        # 测试交易盈亏计算
        if trade_data is not None and not trade_data.empty:
            print("\n测试交易盈亏计算...")
            trading_pnl = analyzer.calculate_trading_pnl()
            if trading_pnl:
                print("✅ 交易盈亏计算成功")
                print(f"   总交易: {trading_pnl['total_trades']} 次")
                print(f"   交易量: {trading_pnl['total_volume']:,.2f} BYB")
                print(f"   净盈亏: {trading_pnl['net_pnl']:,.2f} USDT")
            else:
                print("❌ 交易盈亏计算失败")
        
        # 测试库存分析
        if inventory_data is not None and not inventory_data.empty:
            print("\n测试库存分析...")
            inventory_perf = analyzer.analyze_inventory_performance()
            if inventory_perf:
                print("✅ 库存分析成功")
                print(f"   库存变化: {inventory_perf['inventory_change']:,.2f} BYB")
                print(f"   平均库存: {inventory_perf['avg_inventory']:,.2f} BYB")
            else:
                print("❌ 库存分析失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 盈亏分析器测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def test_lark_notification():
    """测试Lark通知"""
    print("\n" + "=" * 60)
    print("测试Lark通知")
    print("=" * 60)
    
    try:
        from byb_pnl_analyzer import send_lark
        
        test_msg = f"🧪 BYB盈亏分析器测试\n测试时间: {datetime.now(beijing_tz).strftime('%Y-%m-%d %H:%M:%S')}\n这是一条测试消息，请忽略。"
        
        print("发送测试消息到Lark...")
        await send_lark(test_msg, level='info')
        print("✅ Lark通知测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ Lark通知测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主测试函数"""
    print("BYB盈亏分析器功能测试")
    print("=" * 60)
    
    test_results = []
    
    # 1. 测试API连接
    test_results.append(("API连接", test_api_connection()))
    
    # 2. 测试资产计算
    test_results.append(("资产计算", test_asset_calculation()))
    
    # 3. 测试数据加载
    test_results.append(("数据加载", test_data_loading()))
    
    # 4. 测试盈亏分析器
    test_results.append(("盈亏分析器", test_pnl_analyzer()))
    
    # 5. 测试Lark通知
    test_results.append(("Lark通知", await test_lark_notification()))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<15} {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！盈亏分析器可以正常使用。")
    else:
        print("⚠️  部分测试失败，请检查相关配置和文件。")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
