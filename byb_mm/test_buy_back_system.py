#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BYB回购系统测试脚本
功能：测试回购系统的各个功能模块
作者：AI Assistant
创建时间：2025-07-07
"""

import asyncio
import logging
import sys
import traceback
import json
import os
from datetime import datetime
import pytz

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.append(project_root)
sys.path.append(current_dir)

from byb_buy_back import BYBBuyBackSystem

# 时区设置
beijing_tz = pytz.timezone("Asia/Shanghai")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)


async def test_database_connection():
    """测试数据库连接"""
    print("\n" + "=" * 60)
    print("测试数据库连接")
    print("=" * 60)
    
    try:
        system = BYBBuyBackSystem()
        params = system.get_buy_back_params()
        
        print("✅ 数据库连接成功")
        print(f"当前参数:")
        print(f"  ID: {params['id']}")
        print(f"  执行天数: {params['days']}")
        print(f"  总金额: {params['total_amount']:,.2f} BYB")
        print(f"  建议单次: {params['each_amount']:,.2f} BYB")
        print(f"  创建时间: {params['created_at']}")
        
        return True, params
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {str(e)}")
        traceback.print_exc()
        return False, None


async def test_account_balance():
    """测试账户余额获取"""
    print("\n" + "=" * 60)
    print("测试账户余额获取")
    print("=" * 60)
    
    try:
        system = BYBBuyBackSystem()
        balances = system.get_account_balance()
        
        print("✅ 账户余额获取成功")
        print(f"余额信息:")
        
        for coin, balance_info in balances.items():
            if balance_info['total'] > 0:  # 只显示有余额的币种
                print(f"  {coin.upper()}: {balance_info['total']:,.2f} (可用: {balance_info['normal']:,.2f}, 冻结: {balance_info['locked']:,.2f})")
        
        return True, balances
        
    except Exception as e:
        print(f"❌ 账户余额获取失败: {str(e)}")
        traceback.print_exc()
        return False, None


async def test_state_management():
    """测试状态管理"""
    print("\n" + "=" * 60)
    print("测试状态管理")
    print("=" * 60)
    
    try:
        system = BYBBuyBackSystem()
        
        # 测试状态保存和加载
        original_spent = system.total_spent_usdt
        original_history_count = len(system.execution_history)
        
        print(f"原始状态:")
        print(f"  已花费USDT: {original_spent:.2f}")
        print(f"  执行记录数: {original_history_count}")
        
        # 添加测试记录
        test_record = {
            'test_time': datetime.now(beijing_tz).isoformat(),
            'test_data': 'test_execution',
            'status': 'test'
        }
        system.execution_history.append(test_record)
        system.total_spent_usdt += 100.0  # 测试增加100 USDT
        
        # 保存状态
        system.save_state()
        print("✅ 状态保存成功")
        
        # 创建新实例测试加载
        new_system = BYBBuyBackSystem()
        
        print(f"加载后状态:")
        print(f"  已花费USDT: {new_system.total_spent_usdt:.2f}")
        print(f"  执行记录数: {len(new_system.execution_history)}")
        
        # 恢复原始状态
        system.total_spent_usdt = original_spent
        system.execution_history = system.execution_history[:-1]  # 移除测试记录
        system.save_state()
        
        print("✅ 状态管理测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 状态管理测试失败: {str(e)}")
        traceback.print_exc()
        return False


async def test_parameter_change_detection():
    """测试参数变化检测"""
    print("\n" + "=" * 60)
    print("测试参数变化检测")
    print("=" * 60)
    
    try:
        system = BYBBuyBackSystem()
        
        # 获取当前参数
        current_params = system.get_buy_back_params()
        
        # 第一次检查（应该返回True，因为没有历史参数）
        changed1 = system.params_changed(current_params)
        print(f"首次检查参数变化: {changed1} (预期: True)")
        
        # 设置历史参数
        system.last_params = current_params.copy()
        
        # 第二次检查（应该返回False，参数相同）
        changed2 = system.params_changed(current_params)
        print(f"相同参数检查: {changed2} (预期: False)")
        
        # 修改参数测试
        modified_params = current_params.copy()
        modified_params['total_amount'] += 1000
        
        changed3 = system.params_changed(modified_params)
        print(f"修改参数检查: {changed3} (预期: True)")
        
        print("✅ 参数变化检测测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 参数变化检测测试失败: {str(e)}")
        traceback.print_exc()
        return False


async def test_remaining_amount_calculation():
    """测试剩余金额计算"""
    print("\n" + "=" * 60)
    print("测试剩余金额计算")
    print("=" * 60)
    
    try:
        system = BYBBuyBackSystem()
        
        # 测试不同场景
        test_cases = [
            {'total': 10000, 'spent': 0, 'expected': 10000},
            {'total': 10000, 'spent': 3000, 'expected': 7000},
            {'total': 10000, 'spent': 10000, 'expected': 0},
            {'total': 10000, 'spent': 12000, 'expected': 0},  # 超支情况
        ]
        
        for i, case in enumerate(test_cases):
            system.total_spent_usdt = case['spent']
            remaining = system.calculate_remaining_amount(case['total'])
            
            print(f"测试案例 {i+1}:")
            print(f"  总金额: {case['total']:,.2f}, 已花费: {case['spent']:,.2f}")
            print(f"  计算结果: {remaining:,.2f}, 预期: {case['expected']:,.2f}")
            print(f"  结果: {'✅ 正确' if remaining == case['expected'] else '❌ 错误'}")
        
        print("✅ 剩余金额计算测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 剩余金额计算测试失败: {str(e)}")
        traceback.print_exc()
        return False


async def test_system_status():
    """测试系统状态获取"""
    print("\n" + "=" * 60)
    print("测试系统状态获取")
    print("=" * 60)
    
    try:
        system = BYBBuyBackSystem()
        status = system.get_system_status()
        
        print("✅ 系统状态获取成功")
        print(f"系统状态:")
        print(f"  系统运行: {status['system_running']}")
        print(f"  已花费USDT: {status['total_spent_usdt']:,.2f}")
        print(f"  执行记录数: {status['execution_history_count']}")
        print(f"  最后参数: {status['last_params']}")
        print(f"  当前算法: {status['current_algorithm']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 系统状态获取失败: {str(e)}")
        traceback.print_exc()
        return False


async def test_lark_notification():
    """测试Lark通知"""
    print("\n" + "=" * 60)
    print("测试Lark通知")
    print("=" * 60)
    
    try:
        from byb_buy_back import send_lark
        
        test_msg = f"🧪 BYB回购系统测试\n测试时间: {datetime.now(beijing_tz).strftime('%Y-%m-%d %H:%M:%S')}\n这是一条测试消息，请忽略。"
        
        print("发送测试消息到Lark...")
        await send_lark(test_msg, level='info')
        print("✅ Lark通知测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ Lark通知测试失败: {str(e)}")
        traceback.print_exc()
        return False


async def run_all_tests():
    """运行所有测试"""
    print("🧪 BYB回购系统测试套件")
    print("=" * 80)
    
    test_results = {}
    
    # 运行各项测试
    db_success, params = await test_database_connection()
    test_results['database'] = db_success
    
    balance_success, balances = await test_account_balance()
    test_results['balance'] = balance_success
    
    state_success = await test_state_management()
    test_results['state'] = state_success
    
    param_success = await test_parameter_change_detection()
    test_results['parameter'] = param_success
    
    calc_success = await test_remaining_amount_calculation()
    test_results['calculation'] = calc_success
    
    status_success = await test_system_status()
    test_results['status'] = status_success
    
    lark_success = await test_lark_notification()
    test_results['lark'] = lark_success
    
    # 测试结果汇总
    print("\n" + "=" * 80)
    print("测试结果汇总")
    print("=" * 80)
    
    for test_name, success in test_results.items():
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name.ljust(20)}: {status}")
    
    total_tests = len(test_results)
    passed_tests = sum(test_results.values())
    
    print(f"\n总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {total_tests - passed_tests}")
    print(f"通过率: {(passed_tests/total_tests*100):.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试通过！回购系统可以正常使用。")
    else:
        print("\n⚠️  部分测试失败，请检查相关配置。")
    
    return test_results


if __name__ == "__main__":
    print("启动BYB回购系统测试...")
    asyncio.run(run_all_tests())
