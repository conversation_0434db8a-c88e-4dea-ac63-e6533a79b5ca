import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def detect_spikes(df, spike_threshold=2.5):
    """检测插针位置"""
    spike_indices = []
    
    # 计算价格的移动平均和标准差
    window = min(20, len(df) // 4)
    df['price_ma'] = df['close'].rolling(window=window, center=True).mean()
    df['price_std'] = df['close'].rolling(window=window, center=True).std()
    
    # 填充边界值
    df['price_ma'] = df['price_ma'].fillna(method='bfill').fillna(method='ffill')
    df['price_std'] = df['price_std'].fillna(method='bfill').fillna(method='ffill')
    
    for i in range(len(df)):
        row = df.iloc[i]
        ma = row['price_ma']
        std = row['price_std']
        
        # 检测插针
        if (row['high'] > ma + spike_threshold * std or
            row['low'] < ma - spike_threshold * std):
            spike_indices.append(i)
    
    return spike_indices

def optimize_spike(df, i, spike_threshold=2.5):
    """优化单个插针"""
    row = df.iloc[i]
    ma = row['price_ma']
    std = row['price_std']
    
    optimized_high = row['high']
    optimized_low = row['low']

    # 优化高价插针
    if row['high'] > ma + spike_threshold * std:
        if i > 0 and i < len(df) - 1:
            prev_high = df.iloc[i-1]['high']
            next_high = df.iloc[i+1]['high']
            optimized_high = min(row['high'], max(prev_high, next_high, ma + 1.5 * std))
        else:
            optimized_high = min(row['high'], ma + 1.5 * std)

    # 优化低价插针
    if row['low'] < ma - spike_threshold * std:
        if i > 0 and i < len(df) - 1:
            prev_low = df.iloc[i-1]['low']
            next_low = df.iloc[i+1]['low']
            optimized_low = max(row['low'], min(prev_low, next_low, ma - 1.5 * std))
        else:
            optimized_low = max(row['low'], ma - 1.5 * std)
    
    return optimized_high, optimized_low

def plot_spike_comparison(df, title="BYBUSDT K线插针优化对比"):
    """绘制插针优化对比图"""
    
    # 数据预处理
    df_original = df.copy()
    df_optimized = df.copy()
    
    # 转换时间戳
    df_original['datetime'] = pd.to_datetime(df_original['timestamp_sec'], unit='s')
    df_optimized['datetime'] = pd.to_datetime(df_optimized['timestamp_sec'], unit='s')
    
    # 检测插针
    spike_indices = detect_spikes(df_original.copy())
    print(f"检测到 {len(spike_indices)} 个插针位置")
    
    # 为优化函数添加必要的列
    df_with_ma = df_original.copy()
    window = min(20, len(df_with_ma) // 4)
    df_with_ma['price_ma'] = df_with_ma['close'].rolling(window=window, center=True).mean()
    df_with_ma['price_std'] = df_with_ma['close'].rolling(window=window, center=True).std()
    df_with_ma['price_ma'] = df_with_ma['price_ma'].fillna(method='bfill').fillna(method='ffill')
    df_with_ma['price_std'] = df_with_ma['price_std'].fillna(method='bfill').fillna(method='ffill')

    # 优化插针
    for i in spike_indices:
        optimized_high, optimized_low = optimize_spike(df_with_ma, i)
        df_optimized.iloc[i, df_optimized.columns.get_loc('high')] = optimized_high
        df_optimized.iloc[i, df_optimized.columns.get_loc('low')] = optimized_low
    
    # 创建对比图
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12), sharex=True)
    
    # 绘制原始K线图
    plot_candlestick(ax1, df_original, "原始K线图 (含插针)", spike_indices)
    
    # 绘制优化后K线图
    plot_candlestick(ax2, df_optimized, "优化后K线图 (已处理插针)", spike_indices)
    
    # 设置整体标题
    fig.suptitle(title, fontsize=16, fontweight='bold')
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    filename = f'bybusdt_spike_comparison_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"插针对比图已保存为: {filename}")
    
    # 显示图表
    plt.show()
    
    return df_optimized, spike_indices

def plot_candlestick(ax, df, title, spike_indices):
    """绘制单个K线图"""
    
    # 绘制K线
    for i in range(len(df)):
        row = df.iloc[i]
        x = row['datetime']
        open_price = row['open']
        high_price = row['high']
        low_price = row['low']
        close_price = row['close']
        
        # 确定颜色
        color = 'red' if close_price >= open_price else 'green'
        
        # 特殊标记插针位置
        if i in spike_indices:
            line_color = 'orange'
            line_width = 2
            alpha = 1.0
        else:
            line_color = 'black'
            line_width = 0.8
            alpha = 0.8
        
        # 绘制影线
        ax.plot([x, x], [low_price, high_price], color=line_color, linewidth=line_width, alpha=alpha)
        
        # 绘制实体
        body_height = abs(close_price - open_price)
        body_bottom = min(open_price, close_price)
        
        if body_height > 0:
            rect = Rectangle((mdates.date2num(x) - 0.0003, body_bottom), 
                           0.0006, body_height, 
                           facecolor=color, edgecolor=line_color, 
                           linewidth=0.5, alpha=alpha)
            ax.add_patch(rect)
        else:
            # 十字星
            ax.plot([mdates.date2num(x) - 0.0003, mdates.date2num(x) + 0.0003], 
                    [open_price, open_price], color=line_color, linewidth=1, alpha=alpha)
    
    # 添加移动平均线
    if 'price_ma' in df.columns:
        ax.plot(df['datetime'], df['price_ma'], color='blue', linewidth=1, alpha=0.7, label='MA20')
        ax.legend()
    
    # 设置图表
    ax.set_title(title, fontsize=14, fontweight='bold')
    ax.set_ylabel('价格 (USDT)', fontsize=12)
    ax.grid(True, alpha=0.3)
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
    ax.xaxis.set_major_locator(mdates.HourLocator(interval=2))
    
    # 添加统计信息
    stats_text = f"最高: {df['high'].max():.6f} | 最低: {df['low'].min():.6f} | " \
                f"振幅: {((df['high'].max() - df['low'].min()) / df['low'].min() * 100):.2f}%"
    
    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.7),
            verticalalignment='top', fontsize=10)

def analyze_spike_impact(df_original, df_optimized, spike_indices):
    """分析插针优化的影响"""
    print("\n=== 插针优化影响分析 ===")
    
    # 原始数据统计
    original_high = df_original['high'].max()
    original_low = df_original['low'].min()
    original_range = original_high - original_low
    original_volatility = df_original['close'].std()

    # 优化后数据统计
    optimized_high = df_optimized['high'].max()
    optimized_low = df_optimized['low'].min()
    optimized_range = optimized_high - optimized_low
    optimized_volatility = df_optimized['close'].std()
    
    print(f"插针数量: {len(spike_indices)}")
    print(f"原始价格范围: {original_low:.6f} - {original_high:.6f} (振幅: {original_range:.6f})")
    print(f"优化后价格范围: {optimized_low:.6f} - {optimized_high:.6f} (振幅: {optimized_range:.6f})")
    print(f"振幅减少: {original_range - optimized_range:.6f} ({(original_range - optimized_range)/original_range*100:.2f}%)")
    print(f"原始波动率: {original_volatility:.6f}")
    print(f"优化后波动率: {optimized_volatility:.6f}")
    print(f"波动率变化: {optimized_volatility - original_volatility:.6f} ({(optimized_volatility - original_volatility)/original_volatility*100:.2f}%)")
    
    # 分析每个插针的优化效果
    print(f"\n=== 具体插针优化详情 ===")
    for i, spike_idx in enumerate(spike_indices, 1):
        original_row = df_original.iloc[spike_idx]
        optimized_row = df_optimized.iloc[spike_idx]
        
        timestamp = datetime.fromtimestamp(original_row['timestamp_sec'])

        high_change = original_row['high'] - optimized_row['high']
        low_change = optimized_row['low'] - original_row['low']
        
        print(f"{i}. 时间: {timestamp}")
        if high_change > 0:
            print(f"   高价优化: {original_row['high']:.6f} -> {optimized_row['high']:.6f} (减少 {high_change:.6f})")
        if low_change > 0:
            print(f"   低价优化: {original_row['low']:.6f} -> {optimized_row['low']:.6f} (提高 {low_change:.6f})")

def main():
    """主函数"""
    try:
        # 读取K线数据
        print("正在读取K线数据...")
        try:
            df = pd.read_csv('bybusdt_1min_kline_2025-07-03.csv', encoding='utf-8')
        except UnicodeDecodeError:
            try:
                df = pd.read_csv('bybusdt_1min_kline_2025-07-03.csv', encoding='gbk')
            except UnicodeDecodeError:
                df = pd.read_csv('bybusdt_1min_kline_2025-07-03.csv', encoding='latin-1')
        
        print(f"成功读取 {len(df)} 条K线数据")
        
        # 绘制插针对比图
        print("正在生成插针优化对比图...")
        df_optimized, spike_indices = plot_spike_comparison(df)
        
        # 分析优化影响
        analyze_spike_impact(df, df_optimized, spike_indices)
        
        # 保存优化后的数据
        output_file = 'bybusdt_1min_kline_spike_optimized.csv'
        df_optimized.drop(['datetime', 'price_ma', 'price_std'], axis=1, errors='ignore').to_csv(output_file, index=False)
        print(f"\n优化后的K线数据已保存为: {output_file}")
        
    except FileNotFoundError:
        print("错误: 未找到文件 'bybusdt_1min_kline_2025-07-03.csv'")
        print("请确保文件在当前目录下")
    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
