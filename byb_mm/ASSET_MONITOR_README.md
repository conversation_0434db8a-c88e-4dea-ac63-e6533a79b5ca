# BYB资产监控系统

## 概述

BYB资产监控系统用于实时监控BYB交易所的USDT资产，当资产低于设定阈值（500万USDT）时，自动通过Lark（飞书）发送预警通知。

## 功能特性

- ✅ **实时监控**: 每10分钟检查一次USDT资产
- ✅ **智能预警**: 资产低于500万时自动发送Lark通知
- ✅ **防重复预警**: 1小时冷却期，避免频繁发送相同预警
- ✅ **详细日志**: 完整的监控日志记录
- ✅ **异常处理**: 网络异常、API错误等情况的处理
- ✅ **状态恢复**: 资产恢复正常时自动重置预警状态

## 文件结构

```
byb_mm/
├── byb_asset_monitor.py      # 主监控脚本
├── test_asset_monitor.py     # 功能测试脚本
├── start_asset_monitor.sh    # 启动管理脚本
├── ASSET_MONITOR_README.md   # 说明文档
├── byb_asset_monitor.log     # 监控日志文件（运行后生成）
└── asset_monitor.pid         # 进程ID文件（运行时生成）
```

## 配置说明

### 监控参数

在 `byb_asset_monitor.py` 中可以调整以下参数：

```python
ASSET_THRESHOLD = 5000000  # 预警阈值（USDT），默认500万
CHECK_INTERVAL = 600       # 检查间隔（秒），默认10分钟
ALERT_COOLDOWN = 3600      # 预警冷却时间（秒），默认1小时
```

### Lark Webhook

系统使用现有的Lark webhook地址，与其他byb_mm脚本保持一致。

## 使用方法

### 1. 快速启动

```bash
# 进入项目目录
cd /path/to/byb_mm

# 启动监控
./start_asset_monitor.sh start
```

### 2. 管理命令

```bash
# 查看运行状态
./start_asset_monitor.sh status

# 查看实时日志
./start_asset_monitor.sh logs

# 停止监控
./start_asset_monitor.sh stop

# 重启监控
./start_asset_monitor.sh restart

# 运行功能测试
./start_asset_monitor.sh test

# 查看帮助
./start_asset_monitor.sh help
```

### 3. 功能测试

在正式运行前，建议先运行测试：

```bash
# 运行完整测试
python3 test_asset_monitor.py

# 或使用管理脚本
./start_asset_monitor.sh test
```

测试内容包括：
- USDT资产获取功能
- 所有资产信息查看
- Lark通知功能测试

## 监控逻辑

### 预警触发条件

1. **资产检查**: 每10分钟获取一次USDT总资产（可用+冻结）
2. **阈值比较**: 如果总资产 < 500万USDT，触发预警逻辑
3. **冷却机制**: 
   - 首次预警：立即发送
   - 后续预警：距离上次预警超过1小时才发送
   - 资产恢复：重置预警状态

### 预警消息格式

```
🚨 BYB资产预警 🚨
当前USDT资产: 4,500,000.00
预警阈值: 5,000,000.00
资产不足: 500,000.00
检查时间: 2024-06-30 15:30:00
请及时补充资产！
```

## 日志说明

### 日志级别

- **INFO**: 正常运行信息
- **WARNING**: 警告信息（如API调用失败）
- **ERROR**: 错误信息（如异常情况）

### 日志示例

```
2024-06-30 15:30:01 - INFO - BYB资产监控启动
2024-06-30 15:30:02 - INFO - USDT资产详情 - 可用: 4500000.00, 冻结: 0.00, 总计: 4500000.00
2024-06-30 15:30:02 - WARNING - 发送资产预警: 当前资产 4,500,000.00 < 阈值 5,000,000.00
2024-06-30 15:40:02 - INFO - 资产不足但在冷却期内，不重复预警: 4,500,000.00
```

## 故障排除

### 常见问题

1. **无法获取资产信息**
   - 检查API密钥配置
   - 检查网络连接
   - 查看日志中的具体错误信息

2. **Lark通知发送失败**
   - 检查webhook地址是否正确
   - 检查网络连接
   - 确认Lark机器人配置

3. **进程意外退出**
   - 查看日志文件中的错误信息
   - 检查系统资源使用情况
   - 使用restart命令重启监控

### 调试方法

```bash
# 查看详细日志
tail -f byb_asset_monitor.log

# 手动运行测试
python3 test_asset_monitor.py

# 检查进程状态
./start_asset_monitor.sh status
```

## 系统要求

- Python 3.7+
- 依赖包：pandas, aiohttp, backoff, pytz
- 网络连接（访问BYB API和Lark webhook）

## 安全注意事项

1. **API密钥安全**: 确保con_pri.py文件权限设置正确
2. **日志文件**: 定期清理日志文件，避免占用过多磁盘空间
3. **监控频率**: 避免设置过高的检查频率，以免触发API限制

## 维护建议

1. **定期检查**: 每周检查一次监控状态和日志
2. **日志轮转**: 建议配置日志轮转，避免日志文件过大
3. **阈值调整**: 根据实际业务需求调整预警阈值
4. **备份配置**: 定期备份配置文件和脚本

## 联系支持

如有问题或建议，请联系开发团队。
