#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BYB下单算法测试脚本
功能：测试下单算法的各个功能模块
作者：AI Assistant
创建时间：2025-07-07
"""

import asyncio
import logging
import sys
import traceback
from datetime import datetime, timedelta
import pytz

# 添加项目路径
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.append(project_root)
sys.path.append(current_dir)

from byb_order_algorithm import BYBOrderAlgorithm

# 时区设置
beijing_tz = pytz.timezone("Asia/Shanghai")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)


async def test_database_connection():
    """测试数据库连接和参数获取"""
    print("\n" + "=" * 60)
    print("测试数据库连接和参数获取")
    print("=" * 60)
    
    try:
        algorithm = BYBOrderAlgorithm()
        params = algorithm.get_buy_back_params()
        
        print("✅ 数据库连接成功")
        print(f"获取参数: {params}")
        
        return True, params
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {str(e)}")
        traceback.print_exc()
        return False, None


async def test_order_schedule_generation():
    """测试订单计划生成"""
    print("\n" + "=" * 60)
    print("测试订单计划生成")
    print("=" * 60)
    
    try:
        algorithm = BYBOrderAlgorithm()
        
        # 使用测试参数
        test_days = 0.1  # 0.1天 = 2.4小时
        test_amount = 2000  # 2000 BYB
        test_each_amount = 300  # 单次最大300 BYB

        print(f"测试参数: days={test_days}, total_amount={test_amount}, each_amount={test_each_amount}")

        schedule = algorithm.generate_order_schedule(test_days, test_amount, test_each_amount)
        
        print(f"✅ 订单计划生成成功")
        print(f"生成订单数量: {len(schedule)}")
        
        # 显示前5个订单
        print("\n前5个订单详情:")
        for i, order in enumerate(schedule[:5]):
            print(f"订单 {i+1}: {order['amount']:.2f} BYB, 时间: {order['scheduled_time'].strftime('%H:%M:%S')}")
        
        # 统计信息
        total_amount = sum(order['amount'] for order in schedule)
        print(f"\n统计信息:")
        print(f"总金额: {total_amount:.2f} BYB")
        print(f"平均订单大小: {total_amount/len(schedule):.2f} BYB")
        print(f"最小订单: {min(order['amount'] for order in schedule):.2f} BYB")
        print(f"最大订单: {max(order['amount'] for order in schedule):.2f} BYB")
        
        return True, schedule
        
    except Exception as e:
        print(f"❌ 订单计划生成失败: {str(e)}")
        traceback.print_exc()
        return False, None


async def test_lark_notification():
    """测试Lark通知功能"""
    print("\n" + "=" * 60)
    print("测试Lark通知功能")
    print("=" * 60)
    
    try:
        from byb_order_algorithm import send_lark, schedule_lark_message
        
        test_msg = f"🧪 BYB下单算法测试\n测试时间: {datetime.now(beijing_tz).strftime('%Y-%m-%d %H:%M:%S')}\n这是一条测试消息，请忽略。"
        
        print("发送测试消息到Lark...")
        await send_lark(test_msg, level='info')
        print("✅ Lark通知测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ Lark通知测试失败: {str(e)}")
        traceback.print_exc()
        return False


async def test_dry_run():
    """测试模拟运行（不实际下单）"""
    print("\n" + "=" * 60)
    print("测试模拟运行")
    print("=" * 60)
    
    try:
        algorithm = BYBOrderAlgorithm()
        
        # 生成小规模测试计划
        test_days = 0.01  # 0.01天 = 14.4分钟
        test_amount = 500  # 500 BYB
        
        print(f"模拟参数: days={test_days}, total_amount={test_amount}")
        
        schedule = algorithm.generate_order_schedule(test_days, test_amount)
        print(f"生成 {len(schedule)} 个订单")
        
        # 模拟执行（不实际下单）
        print("\n开始模拟执行:")
        for i, order in enumerate(schedule[:3]):  # 只模拟前3个订单
            print(f"模拟订单 {i+1}: {order['amount']:.2f} BYB 在 {order['scheduled_time'].strftime('%H:%M:%S')}")
            
            # 模拟等待时间（缩短为1秒）
            await asyncio.sleep(1)
            
            # 模拟订单执行结果
            order['status'] = 'simulated'
            order['executed_time'] = datetime.now(beijing_tz)
            print(f"  ✅ 模拟执行成功")
        
        print("✅ 模拟运行测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 模拟运行测试失败: {str(e)}")
        traceback.print_exc()
        return False


async def test_status_monitoring():
    """测试状态监控功能"""
    print("\n" + "=" * 60)
    print("测试状态监控功能")
    print("=" * 60)
    
    try:
        algorithm = BYBOrderAlgorithm()
        
        # 初始状态
        status = algorithm.get_status()
        print("初始状态:")
        print(f"  运行中: {status['is_running']}")
        print(f"  总订单数: {status['total_orders']}")
        print(f"  已执行订单: {status['orders_executed']}")
        print(f"  执行进度: {status['progress']:.1f}%")
        
        # 生成测试计划
        schedule = algorithm.generate_order_schedule(0.01, 300)
        
        # 更新状态
        status = algorithm.get_status()
        print("\n生成计划后状态:")
        print(f"  总订单数: {status['total_orders']}")
        print(f"  执行进度: {status['progress']:.1f}%")
        
        print("✅ 状态监控测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 状态监控测试失败: {str(e)}")
        traceback.print_exc()
        return False


async def run_all_tests():
    """运行所有测试"""
    print("🧪 BYB下单算法测试套件")
    print("=" * 60)
    
    test_results = {}
    
    # 测试数据库连接
    db_success, params = await test_database_connection()
    test_results['database'] = db_success
    
    # 测试订单计划生成
    schedule_success, schedule = await test_order_schedule_generation()
    test_results['schedule'] = schedule_success
    
    # 测试Lark通知
    lark_success = await test_lark_notification()
    test_results['lark'] = lark_success
    
    # 测试模拟运行
    dry_run_success = await test_dry_run()
    test_results['dry_run'] = dry_run_success
    
    # 测试状态监控
    status_success = await test_status_monitoring()
    test_results['status'] = status_success
    
    # 测试结果汇总
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    for test_name, success in test_results.items():
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name.ljust(20)}: {status}")
    
    total_tests = len(test_results)
    passed_tests = sum(test_results.values())
    
    print(f"\n总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {total_tests - passed_tests}")
    print(f"通过率: {(passed_tests/total_tests*100):.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试通过！算法可以正常使用。")
    else:
        print("\n⚠️  部分测试失败，请检查相关配置。")
    
    return test_results


if __name__ == "__main__":
    print("启动BYB下单算法测试...")
    asyncio.run(run_all_tests())
