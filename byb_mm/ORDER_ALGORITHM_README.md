# BYB智能下单算法

## 概述

BYB智能下单算法是一个自动化交易工具，能够从数据库获取参数，在指定的时间范围内随机分布执行买单操作。该算法设计用于回购场景，支持智能订单分配和实时监控。

## 主要功能

### 🎯 核心功能
- **数据库参数获取**: 自动从 `buy_back_params` 表获取交易参数
- **智能时间分布**: 在指定天数内随机分布下单时间点
- **订单量随机化**: 每次下单量在200-800 BYB之间随机
- **实时监控**: 提供执行进度和状态监控
- **异常处理**: 完善的错误处理和重试机制
- **通知系统**: 集成Lark通知，实时推送执行状态

### 📊 算法特性
- **时间随机化**: 下单间隔1-60分钟随机，避免规律性
- **订单大小优化**: 单次下单200-800 BYB，符合要求
- **进度跟踪**: 实时记录执行进度和成功率
- **日志记录**: 详细的执行日志和JSON格式的结果保存

## 文件结构

```
byb_mm/
├── byb_order_algorithm.py      # 主算法文件
├── test_order_algorithm.py     # 测试脚本
├── ORDER_ALGORITHM_README.md   # 使用说明（本文件）
└── byb_buy_back.py            # 数据库参数获取（已存在）
```

## 数据库配置

### 数据库表结构
算法从 `buy_back_params` 表读取参数：

```sql
CREATE TABLE buy_back_params (
    id INT PRIMARY KEY AUTO_INCREMENT,
    days DECIMAL(10,2) NOT NULL,        -- 执行天数
    total_amount DECIMAL(15,2) NOT NULL, -- 总下单量（BYB）
    each_amount DECIMAL(15,2),           -- 单次下单量（可选）
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 示例数据
```sql
INSERT INTO buy_back_params (days, total_amount, each_amount) 
VALUES (7.0, 50000.0, 500.0);
```

## 配置参数

### 交易配置
```python
TRADING_CONFIG = {
    "symbol": "bybusdt",        # 交易对
    "side": "BUY",              # 买单
    "type": 2,                  # 市价单
    "min_order_size": 200,      # 最小单次下单量（BYB）
    "max_order_size": 800,      # 最大单次下单量（BYB）
    "min_interval": 60,         # 最小下单间隔（秒）
    "max_interval": 3600,       # 最大下单间隔（秒）
}
```

### 数据库配置
```python
DB_CONFIG = {
    "host": "127.0.0.1",
    "port": 3306,
    "user": "weili",
    "password": "12345678",
    "database": "buy_back"
}
```

## 使用方法

### 1. 基本使用

```bash
# 运行主算法
cd /path/to/byb_mm
python byb_order_algorithm.py
```

### 2. 测试功能

```bash
# 运行测试套件
python test_order_algorithm.py
```

### 3. 程序化使用

```python
import asyncio
from byb_order_algorithm import BYBOrderAlgorithm

async def main():
    # 创建算法实例
    algorithm = BYBOrderAlgorithm()
    
    # 运行算法
    await algorithm.run_algorithm()
    
    # 获取执行状态
    status = algorithm.get_status()
    print(f"执行进度: {status['progress']:.1f}%")

# 运行
asyncio.run(main())
```

## 算法流程

### 1. 初始化阶段
1. 连接数据库获取参数（days, total_amount）
2. 初始化交易客户端
3. 发送启动通知到Lark

### 2. 计划生成阶段
1. 根据总时间范围和总金额生成订单计划
2. 随机分配每个订单的执行时间
3. 随机分配每个订单的下单量（200-800 BYB）

### 3. 执行阶段
1. 按时间顺序等待并执行每个订单
2. 使用市价单进行买入操作
3. 记录执行结果和异常情况
4. 定期发送进度通知

### 4. 完成阶段
1. 统计执行结果
2. 保存详细日志到JSON文件
3. 发送完成通知到Lark

## 监控和通知

### Lark通知类型
- **启动通知**: 算法开始执行时发送
- **进度通知**: 每10个订单发送一次进度更新
- **异常通知**: 订单执行失败时发送
- **完成通知**: 算法执行完成时发送统计信息

### 日志文件
- `byb_order_algorithm.log`: 运行日志
- `byb_order_execution_YYYYMMDD_HHMMSS.json`: 执行结果详细记录

## 安全特性

### 风险控制
- **订单大小限制**: 单次下单量控制在200-800 BYB
- **时间分散**: 避免集中下单造成市场冲击
- **异常处理**: 完善的错误处理和恢复机制
- **执行监控**: 实时监控执行状态和成功率

### 故障恢复
- **重试机制**: API调用失败时自动重试
- **状态保存**: 执行状态实时保存，支持中断恢复
- **错误通知**: 异常情况及时通知管理员

## 性能优化

### 执行效率
- **异步执行**: 使用asyncio提高并发性能
- **API限制**: 控制API调用频率避免限制
- **内存优化**: 合理管理订单数据和日志

### 资源使用
- **数据库连接**: 按需连接，及时释放
- **网络请求**: 合理的超时和重试设置
- **日志管理**: 分级日志记录，避免过度占用存储

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库配置信息
   - 确认数据库服务运行状态
   - 验证用户权限

2. **API调用失败**
   - 检查API密钥配置
   - 确认网络连接状态
   - 查看API限制情况

3. **Lark通知失败**
   - 检查webhook地址
   - 确认网络连接
   - 查看Lark机器人配置

### 调试方法

```bash
# 查看运行日志
tail -f byb_order_algorithm.log

# 运行测试检查各模块
python test_order_algorithm.py

# 检查数据库连接
python -c "from byb_order_algorithm import BYBOrderAlgorithm; print(BYBOrderAlgorithm().get_buy_back_params())"
```

## 注意事项

1. **资金安全**: 确保账户有足够资金执行订单
2. **市场时间**: 注意交易所开放时间
3. **网络稳定**: 确保网络连接稳定
4. **监控运行**: 定期检查执行状态和日志
5. **参数合理**: 确保数据库参数设置合理

## 更新日志

### v1.0.0 (2025-07-07)
- 初始版本发布
- 实现基本的智能下单功能
- 集成数据库参数获取
- 添加Lark通知系统
- 完善测试套件
