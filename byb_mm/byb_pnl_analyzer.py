#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BYB做市盈亏统计分析脚本
分析交易盈亏、资产变化、库存成本等关键指标
"""

import asyncio
import logging
import pandas as pd
import numpy as np
import sys
import time
import json
import os
from datetime import datetime, timedelta
import pytz
import traceback
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib import rcParams
import seaborn as sns
import backoff
import aiohttp

# 添加项目路径
sys.path.append(r'/home/<USER>/byb_mm/')

# 导入必要的模块
from byex.spot.trade import SpotTrade
from byex.spot.market import Spot
import con_pri

# 设置北京时区
beijing_tz = pytz.timezone("Asia/Shanghai")

# 配置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
rcParams['axes.unicode_minus'] = False

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('byb_pnl_analyzer.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# 初始化API客户端
spot_client = SpotTrade(con_pri.api_key, con_pri.api_secret)
spot_market = Spot()

# 常量配置
INITIAL_BYB_INVENTORY = 15000000  # 初始BYB库存
INITIAL_USDT_INVENTORY = 1500000  # 初始USDT库存
TRADING_FEE_RATE = 0.001  # 交易手续费率（0.1%）


@backoff.on_exception(backoff.expo, Exception, max_tries=3)
async def send_lark(msg, level='info'):
    """发送消息到Lark（飞书）"""
    headers = {'Content-Type': 'application/json'}
    payload_message = {
        "msg_type": "text",
        "content": {
            "text": f"【{level.upper()}】BYB盈亏分析\n{str(msg)}"
        }
    }
    webhook = 'https://open.larksuite.com/open-apis/bot/v2/hook/230d3321-92cb-44b4-8dcc-a77b2e2c4c2a'
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(webhook, data=json.dumps(payload_message), headers=headers, timeout=10) as response:
                res = await response.json()
                statuscode = res.get('StatusCode', 404)
                if statuscode != 0 or res["StatusMessage"] != 'success':
                    logging.error(f"Lark webhook response error: {res}")
    except Exception as e:
        logging.error(f"Lark notification error for message '{msg}': {str(e)}")


class BYBPnLAnalyzer:
    """BYB做市盈亏分析器"""
    
    def __init__(self):
        self.current_time = datetime.now(beijing_tz)
        self.trade_history_file = 'trade_history.csv'
        self.inventory_history_file = 'inventory_history.json'
        self.pnl_report_file = f'byb_pnl_report_{self.current_time.strftime("%Y%m%d_%H%M%S")}.csv'
        
        # 数据存储
        self.trade_data = None
        self.inventory_data = None
        self.current_assets = {}
        self.pnl_summary = {}
        
    def get_current_assets(self):
        """获取当前资产"""
        try:
            account = spot_client.account()
            if not account or 'coin_list' not in account:
                logging.error("获取账户信息失败")
                return None
                
            df_account = pd.DataFrame(account['coin_list'])
            
            assets = {}
            for coin in ['byb', 'usdt']:
                coin_assets = df_account[df_account['coin'] == coin]
                if not coin_assets.empty:
                    normal = float(coin_assets['normal'].iloc[0])
                    locked = float(coin_assets['locked'].iloc[0])
                    total = normal + locked
                    assets[coin] = {
                        'normal': normal,
                        'locked': locked,
                        'total': total
                    }
                    logging.info(f"{coin.upper()}资产 - 可用: {normal:,.2f}, 冻结: {locked:,.2f}, 总计: {total:,.2f}")
                else:
                    assets[coin] = {'normal': 0, 'locked': 0, 'total': 0}
                    
            self.current_assets = assets
            return assets
            
        except Exception as e:
            logging.error(f"获取当前资产失败: {str(e)}\n{traceback.format_exc()}")
            return None
    
    def load_trade_history(self):
        """加载交易历史数据"""
        try:
            if not os.path.exists(self.trade_history_file):
                logging.warning(f"交易历史文件 {self.trade_history_file} 不存在")
                return None
                
            # 读取CSV文件
            df = pd.read_csv(self.trade_history_file, encoding='utf-8')
            
            # 处理中文列名
            if '成交时间' in df.columns:
                df['成交时间'] = pd.to_datetime(df['成交时间'])
                df = df.rename(columns={
                    '成交ID': 'trade_id',
                    '成交时间': 'timestamp',
                    '方向': 'side',
                    '成交价格': 'price',
                    '成交数量': 'size',
                    '中间价': 'mid_price',
                    '当前仓位': 'inventory',
                    '当前盈亏': 'pnl',
                    '角色': 'role',
                    '库存成本': 'inventory_cost'
                })
            else:
                # 如果是英文列名，转换时间戳
                df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            # 确保数值列的类型正确
            numeric_columns = ['price', 'size', 'mid_price', 'inventory', 'pnl', 'inventory_cost']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 按时间排序
            df = df.sort_values('timestamp').reset_index(drop=True)
            
            self.trade_data = df
            logging.info(f"成功加载 {len(df)} 条交易记录")
            return df
            
        except Exception as e:
            logging.error(f"加载交易历史失败: {str(e)}\n{traceback.format_exc()}")
            return None
    
    def load_inventory_history(self):
        """加载库存历史数据"""
        try:
            if not os.path.exists(self.inventory_history_file):
                logging.warning(f"库存历史文件 {self.inventory_history_file} 不存在")
                return None
                
            with open(self.inventory_history_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 转换为DataFrame
            df = pd.DataFrame(data)
            df['datetime'] = pd.to_datetime(df['timestamp'], unit='s')
            df = df.sort_values('timestamp').reset_index(drop=True)
            
            self.inventory_data = df
            logging.info(f"成功加载 {len(df)} 条库存记录")
            return df
            
        except Exception as e:
            logging.error(f"加载库存历史失败: {str(e)}\n{traceback.format_exc()}")
            return None
    
    def calculate_asset_pnl(self):
        """计算资产盈亏"""
        try:
            if not self.current_assets:
                logging.error("当前资产数据为空")
                return None
            
            # 计算资产变化
            byb_current = self.current_assets.get('byb', {}).get('total', 0)
            usdt_current = self.current_assets.get('usdt', {}).get('total', 0)
            
            byb_change = byb_current - INITIAL_BYB_INVENTORY
            usdt_change = usdt_current - INITIAL_USDT_INVENTORY
            
            # 获取当前BYB价格
            try:
                ticker = spot_market.get_ticker('bybusdt')
                if ticker is not None:
                    current_price = float(ticker['last'])
                else:
                    current_price = 0
            except:
                current_price = 0
                logging.warning("无法获取当前BYB价格")
            
            # 计算总盈亏（以USDT计价）
            byb_value_change = byb_change * current_price
            total_pnl_usdt = usdt_change + byb_value_change
            
            asset_pnl = {
                'byb_initial': INITIAL_BYB_INVENTORY,
                'byb_current': byb_current,
                'byb_change': byb_change,
                'usdt_initial': INITIAL_USDT_INVENTORY,
                'usdt_current': usdt_current,
                'usdt_change': usdt_change,
                'current_price': current_price,
                'byb_value_change': byb_value_change,
                'total_pnl_usdt': total_pnl_usdt,
                'calculation_time': self.current_time
            }
            
            return asset_pnl
            
        except Exception as e:
            logging.error(f"计算资产盈亏失败: {str(e)}\n{traceback.format_exc()}")
            return None
    
    def calculate_trading_pnl(self, start_date=None, end_date=None):
        """计算交易盈亏"""
        try:
            if self.trade_data is None or self.trade_data.empty:
                logging.warning("交易数据为空")
                return None
            
            df = self.trade_data.copy()
            
            # 时间过滤
            if start_date:
                df = df[df['timestamp'] >= start_date]
            if end_date:
                df = df[df['timestamp'] <= end_date]
            
            if df.empty:
                logging.warning("指定时间段内无交易数据")
                return None
            
            # 计算交易统计
            total_trades = len(df)
            buy_trades = len(df[df['side'] == 'buy'])
            sell_trades = len(df[df['side'] == 'sell'])
            
            # 计算交易量
            total_volume = df['size'].sum()
            buy_volume = df[df['side'] == 'buy']['size'].sum()
            sell_volume = df[df['side'] == 'sell']['size'].sum()
            
            # 计算交易额（以USDT计）
            df['trade_value'] = df['price'] * df['size']
            total_value = df['trade_value'].sum()
            buy_value = df[df['side'] == 'buy']['trade_value'].sum()
            sell_value = df[df['side'] == 'sell']['trade_value'].sum()
            
            # 计算手续费
            total_fees = total_value * TRADING_FEE_RATE
            
            # 计算最终盈亏
            final_pnl = df['pnl'].iloc[-1] if not df.empty else 0
            
            # 计算平均价格
            avg_buy_price = df[df['side'] == 'buy']['price'].mean() if buy_trades > 0 else 0
            avg_sell_price = df[df['side'] == 'sell']['price'].mean() if sell_trades > 0 else 0
            
            trading_pnl = {
                'period_start': df['timestamp'].min(),
                'period_end': df['timestamp'].max(),
                'total_trades': total_trades,
                'buy_trades': buy_trades,
                'sell_trades': sell_trades,
                'total_volume': total_volume,
                'buy_volume': buy_volume,
                'sell_volume': sell_volume,
                'total_value': total_value,
                'buy_value': buy_value,
                'sell_value': sell_value,
                'avg_buy_price': avg_buy_price,
                'avg_sell_price': avg_sell_price,
                'total_fees': total_fees,
                'final_pnl': final_pnl,
                'net_pnl': final_pnl - total_fees
            }
            
            return trading_pnl
            
        except Exception as e:
            logging.error(f"计算交易盈亏失败: {str(e)}\n{traceback.format_exc()}")
            return None

    def analyze_inventory_performance(self):
        """分析库存表现"""
        try:
            if self.inventory_data is None or self.inventory_data.empty:
                logging.warning("库存数据为空")
                return None

            df = self.inventory_data.copy()

            # 计算库存统计
            initial_inventory = df['inventory'].iloc[0]
            final_inventory = df['inventory'].iloc[-1]
            max_inventory = df['inventory'].max()
            min_inventory = df['inventory'].min()
            avg_inventory = df['inventory'].mean()

            # 计算库存变化
            inventory_change = final_inventory - initial_inventory
            inventory_volatility = df['inventory'].std()

            # 计算库存变化频率
            df['inventory_diff'] = df['inventory'].diff()
            inventory_changes = len(df[df['inventory_diff'] != 0])

            inventory_performance = {
                'period_start': df['datetime'].min(),
                'period_end': df['datetime'].max(),
                'initial_inventory': initial_inventory,
                'final_inventory': final_inventory,
                'inventory_change': inventory_change,
                'max_inventory': max_inventory,
                'min_inventory': min_inventory,
                'avg_inventory': avg_inventory,
                'inventory_volatility': inventory_volatility,
                'inventory_changes': inventory_changes,
                'total_records': len(df)
            }

            return inventory_performance

        except Exception as e:
            logging.error(f"分析库存表现失败: {str(e)}\n{traceback.format_exc()}")
            return None

    def generate_time_series_analysis(self, period='1D'):
        """生成时间序列分析"""
        try:
            if self.trade_data is None or self.trade_data.empty:
                logging.warning("交易数据为空")
                return None

            df = self.trade_data.copy()
            df.set_index('timestamp', inplace=True)

            # 按时间段聚合
            agg_data = df.groupby(pd.Grouper(freq=period)).agg({
                'size': 'sum',
                'price': 'mean',
                'pnl': 'last',
                'inventory': 'last'
            }).reset_index()

            # 计算每日盈亏变化
            agg_data['pnl_change'] = agg_data['pnl'].diff()
            agg_data['inventory_change'] = agg_data['inventory'].diff()

            return agg_data

        except Exception as e:
            logging.error(f"生成时间序列分析失败: {str(e)}\n{traceback.format_exc()}")
            return None

    def create_visualizations(self):
        """创建可视化图表"""
        try:
            if self.trade_data is None or self.trade_data.empty:
                logging.warning("交易数据为空，无法生成图表")
                return None

            # 设置图表样式
            plt.style.use('seaborn-v0_8')
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))
            fig.suptitle('BYB做市策略盈亏分析', fontsize=16, fontweight='bold')

            df = self.trade_data.copy()

            # 1. 盈亏趋势图
            axes[0, 0].plot(df['timestamp'], df['pnl'], linewidth=2, color='blue')
            axes[0, 0].set_title('累计盈亏趋势')
            axes[0, 0].set_xlabel('时间')
            axes[0, 0].set_ylabel('盈亏 (USDT)')
            axes[0, 0].grid(True, alpha=0.3)
            axes[0, 0].xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
            axes[0, 0].tick_params(axis='x', rotation=45)

            # 2. 库存变化图
            axes[0, 1].plot(df['timestamp'], df['inventory'], linewidth=2, color='green')
            axes[0, 1].set_title('库存变化趋势')
            axes[0, 1].set_xlabel('时间')
            axes[0, 1].set_ylabel('库存 (BYB)')
            axes[0, 1].grid(True, alpha=0.3)
            axes[0, 1].xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
            axes[0, 1].tick_params(axis='x', rotation=45)

            # 3. 价格分布图
            buy_prices = df[df['side'] == 'buy']['price']
            sell_prices = df[df['side'] == 'sell']['price']

            axes[1, 0].hist(buy_prices, bins=50, alpha=0.7, label='买入价格', color='red')
            axes[1, 0].hist(sell_prices, bins=50, alpha=0.7, label='卖出价格', color='green')
            axes[1, 0].set_title('交易价格分布')
            axes[1, 0].set_xlabel('价格 (USDT)')
            axes[1, 0].set_ylabel('频次')
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3)

            # 4. 交易量统计
            hourly_volume = df.groupby(df['timestamp'].dt.hour)['size'].sum()
            axes[1, 1].bar(hourly_volume.index, hourly_volume.values, color='orange', alpha=0.7)
            axes[1, 1].set_title('小时交易量分布')
            axes[1, 1].set_xlabel('小时')
            axes[1, 1].set_ylabel('交易量 (BYB)')
            axes[1, 1].grid(True, alpha=0.3)

            plt.tight_layout()

            # 保存图表
            chart_filename = f'byb_pnl_analysis_{self.current_time.strftime("%Y%m%d_%H%M%S")}.png'
            plt.savefig(chart_filename, dpi=300, bbox_inches='tight')
            logging.info(f"图表已保存: {chart_filename}")

            return chart_filename

        except Exception as e:
            logging.error(f"创建可视化图表失败: {str(e)}\n{traceback.format_exc()}")
            return None

    def generate_summary_report(self):
        """生成汇总报告"""
        try:
            # 获取各项分析结果
            asset_pnl = self.calculate_asset_pnl()
            trading_pnl = self.calculate_trading_pnl()
            inventory_perf = self.analyze_inventory_performance()

            # 构建报告
            report = {
                'report_time': self.current_time.strftime('%Y-%m-%d %H:%M:%S'),
                'asset_analysis': asset_pnl,
                'trading_analysis': trading_pnl,
                'inventory_analysis': inventory_perf
            }

            self.pnl_summary = report
            return report

        except Exception as e:
            logging.error(f"生成汇总报告失败: {str(e)}\n{traceback.format_exc()}")
            return None

    def print_summary_report(self):
        """打印汇总报告"""
        try:
            if not self.pnl_summary:
                logging.warning("汇总报告为空")
                return

            print("\n" + "="*80)
            print("BYB做市策略盈亏分析报告".center(80))
            print("="*80)
            print(f"报告时间: {self.pnl_summary['report_time']}")

            # 资产分析
            if self.pnl_summary.get('asset_analysis'):
                asset = self.pnl_summary['asset_analysis']
                print(f"\n📊 资产分析")
                print("-" * 40)
                print(f"BYB资产变化: {asset['byb_change']:,.2f} ({asset['byb_current']:,.2f} - {asset['byb_initial']:,.2f})")
                print(f"USDT资产变化: {asset['usdt_change']:,.2f}")
                print(f"当前BYB价格: {asset['current_price']:.6f} USDT")
                print(f"BYB价值变化: {asset['byb_value_change']:,.2f} USDT")
                print(f"总盈亏(USDT): {asset['total_pnl_usdt']:,.2f}")

            # 交易分析
            if self.pnl_summary.get('trading_analysis'):
                trading = self.pnl_summary['trading_analysis']
                print(f"\n📈 交易分析")
                print("-" * 40)
                print(f"交易期间: {trading['period_start'].strftime('%Y-%m-%d %H:%M')} ~ {trading['period_end'].strftime('%Y-%m-%d %H:%M')}")
                print(f"总交易次数: {trading['total_trades']:,} (买入: {trading['buy_trades']:,}, 卖出: {trading['sell_trades']:,})")
                print(f"总交易量: {trading['total_volume']:,.2f} BYB")
                print(f"总交易额: {trading['total_value']:,.2f} USDT")
                print(f"平均买入价: {trading['avg_buy_price']:.6f} USDT")
                print(f"平均卖出价: {trading['avg_sell_price']:.6f} USDT")
                print(f"交易手续费: {trading['total_fees']:,.2f} USDT")
                print(f"交易盈亏: {trading['final_pnl']:,.2f} USDT")
                print(f"净盈亏: {trading['net_pnl']:,.2f} USDT")

            # 库存分析
            if self.pnl_summary.get('inventory_analysis'):
                inventory = self.pnl_summary['inventory_analysis']
                print(f"\n📦 库存分析")
                print("-" * 40)
                print(f"库存期间: {inventory['period_start'].strftime('%Y-%m-%d %H:%M')} ~ {inventory['period_end'].strftime('%Y-%m-%d %H:%M')}")
                print(f"库存变化: {inventory['inventory_change']:,.2f} BYB")
                print(f"最大库存: {inventory['max_inventory']:,.2f} BYB")
                print(f"最小库存: {inventory['min_inventory']:,.2f} BYB")
                print(f"平均库存: {inventory['avg_inventory']:,.2f} BYB")
                print(f"库存波动: {inventory['inventory_volatility']:,.2f} BYB")
                print(f"库存变化次数: {inventory['inventory_changes']:,}")

            print("\n" + "="*80)

        except Exception as e:
            logging.error(f"打印汇总报告失败: {str(e)}\n{traceback.format_exc()}")

    def export_to_csv(self):
        """导出详细数据到CSV"""
        try:
            if not self.pnl_summary:
                logging.warning("无数据可导出")
                return None

            # 准备导出数据
            export_data = []

            # 添加资产数据
            if self.pnl_summary.get('asset_analysis'):
                asset = self.pnl_summary['asset_analysis']
                export_data.append({
                    'category': '资产分析',
                    'metric': 'BYB初始资产',
                    'value': asset['byb_initial'],
                    'unit': 'BYB'
                })
                export_data.append({
                    'category': '资产分析',
                    'metric': 'BYB当前资产',
                    'value': asset['byb_current'],
                    'unit': 'BYB'
                })
                export_data.append({
                    'category': '资产分析',
                    'metric': 'BYB资产变化',
                    'value': asset['byb_change'],
                    'unit': 'BYB'
                })
                export_data.append({
                    'category': '资产分析',
                    'metric': 'USDT资产变化',
                    'value': asset['usdt_change'],
                    'unit': 'USDT'
                })
                export_data.append({
                    'category': '资产分析',
                    'metric': '总盈亏',
                    'value': asset['total_pnl_usdt'],
                    'unit': 'USDT'
                })

            # 添加交易数据
            if self.pnl_summary.get('trading_analysis'):
                trading = self.pnl_summary['trading_analysis']
                export_data.extend([
                    {'category': '交易分析', 'metric': '总交易次数', 'value': trading['total_trades'], 'unit': '次'},
                    {'category': '交易分析', 'metric': '买入交易次数', 'value': trading['buy_trades'], 'unit': '次'},
                    {'category': '交易分析', 'metric': '卖出交易次数', 'value': trading['sell_trades'], 'unit': '次'},
                    {'category': '交易分析', 'metric': '总交易量', 'value': trading['total_volume'], 'unit': 'BYB'},
                    {'category': '交易分析', 'metric': '总交易额', 'value': trading['total_value'], 'unit': 'USDT'},
                    {'category': '交易分析', 'metric': '平均买入价', 'value': trading['avg_buy_price'], 'unit': 'USDT'},
                    {'category': '交易分析', 'metric': '平均卖出价', 'value': trading['avg_sell_price'], 'unit': 'USDT'},
                    {'category': '交易分析', 'metric': '交易手续费', 'value': trading['total_fees'], 'unit': 'USDT'},
                    {'category': '交易分析', 'metric': '交易盈亏', 'value': trading['final_pnl'], 'unit': 'USDT'},
                    {'category': '交易分析', 'metric': '净盈亏', 'value': trading['net_pnl'], 'unit': 'USDT'}
                ])

            # 添加库存数据
            if self.pnl_summary.get('inventory_analysis'):
                inventory = self.pnl_summary['inventory_analysis']
                export_data.extend([
                    {'category': '库存分析', 'metric': '初始库存', 'value': inventory['initial_inventory'], 'unit': 'BYB'},
                    {'category': '库存分析', 'metric': '最终库存', 'value': inventory['final_inventory'], 'unit': 'BYB'},
                    {'category': '库存分析', 'metric': '库存变化', 'value': inventory['inventory_change'], 'unit': 'BYB'},
                    {'category': '库存分析', 'metric': '最大库存', 'value': inventory['max_inventory'], 'unit': 'BYB'},
                    {'category': '库存分析', 'metric': '最小库存', 'value': inventory['min_inventory'], 'unit': 'BYB'},
                    {'category': '库存分析', 'metric': '平均库存', 'value': inventory['avg_inventory'], 'unit': 'BYB'},
                    {'category': '库存分析', 'metric': '库存波动', 'value': inventory['inventory_volatility'], 'unit': 'BYB'},
                    {'category': '库存分析', 'metric': '库存变化次数', 'value': inventory['inventory_changes'], 'unit': '次'}
                ])

            # 创建DataFrame并导出
            df_export = pd.DataFrame(export_data)
            df_export['report_time'] = self.current_time.strftime('%Y-%m-%d %H:%M:%S')

            df_export.to_csv(self.pnl_report_file, index=False, encoding='utf-8-sig')
            logging.info(f"盈亏报告已导出: {self.pnl_report_file}")

            return self.pnl_report_file

        except Exception as e:
            logging.error(f"导出CSV失败: {str(e)}\n{traceback.format_exc()}")
            return None

    async def send_report_to_lark(self):
        """发送报告到Lark"""
        try:
            if not self.pnl_summary:
                logging.warning("无报告可发送")
                return

            # 构建Lark消息
            msg_lines = ["📊 BYB做市盈亏分析报告"]
            msg_lines.append(f"⏰ 报告时间: {self.pnl_summary['report_time']}")

            # 资产分析摘要
            if self.pnl_summary.get('asset_analysis'):
                asset = self.pnl_summary['asset_analysis']
                msg_lines.append(f"\n💰 资产分析:")
                msg_lines.append(f"• BYB变化: {asset['byb_change']:,.0f}")
                msg_lines.append(f"• USDT变化: {asset['usdt_change']:,.2f}")
                msg_lines.append(f"• 总盈亏: {asset['total_pnl_usdt']:,.2f} USDT")

            # 交易分析摘要
            if self.pnl_summary.get('trading_analysis'):
                trading = self.pnl_summary['trading_analysis']
                msg_lines.append(f"\n📈 交易分析:")
                msg_lines.append(f"• 总交易: {trading['total_trades']:,}次")
                msg_lines.append(f"• 交易量: {trading['total_volume']:,.0f} BYB")
                msg_lines.append(f"• 净盈亏: {trading['net_pnl']:,.2f} USDT")

            # 库存分析摘要
            if self.pnl_summary.get('inventory_analysis'):
                inventory = self.pnl_summary['inventory_analysis']
                msg_lines.append(f"\n📦 库存分析:")
                msg_lines.append(f"• 库存变化: {inventory['inventory_change']:,.0f} BYB")
                msg_lines.append(f"• 平均库存: {inventory['avg_inventory']:,.0f} BYB")

            message = "\n".join(msg_lines)
            await send_lark(message, level='info')
            logging.info("盈亏报告已发送到Lark")

        except Exception as e:
            logging.error(f"发送报告到Lark失败: {str(e)}\n{traceback.format_exc()}")

    async def run_full_analysis(self, send_to_lark=False, create_charts=False):
        """运行完整分析"""
        try:
            logging.info("开始BYB盈亏分析...")

            # 1. 获取当前资产
            logging.info("获取当前资产...")
            self.get_current_assets()

            # 2. 加载历史数据
            logging.info("加载交易历史...")
            self.load_trade_history()

            logging.info("加载库存历史...")
            self.load_inventory_history()

            # 3. 生成分析报告
            logging.info("生成分析报告...")
            self.generate_summary_report()

            # 4. 打印报告
            self.print_summary_report()

            # 5. 导出CSV
            logging.info("导出CSV报告...")
            csv_file = self.export_to_csv()

            # 6. 创建图表（可选）
            chart_file = None
            if create_charts:
                logging.info("创建可视化图表...")
                chart_file = self.create_visualizations()

            # 7. 发送到Lark（可选）
            if send_to_lark:
                logging.info("发送报告到Lark...")
                await self.send_report_to_lark()

            logging.info("BYB盈亏分析完成!")

            return {
                'summary': self.pnl_summary,
                'csv_file': csv_file,
                'chart_file': chart_file
            }

        except Exception as e:
            logging.error(f"运行完整分析失败: {str(e)}\n{traceback.format_exc()}")
            return None


async def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='BYB做市盈亏分析工具')
    parser.add_argument('--lark', action='store_true', help='发送报告到Lark')
    parser.add_argument('--charts', action='store_true', help='生成可视化图表')
    parser.add_argument('--start-date', type=str, help='开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end-date', type=str, help='结束日期 (YYYY-MM-DD)')
    parser.add_argument('--quick', action='store_true', help='快速分析（仅当前资产）')

    args = parser.parse_args()

    try:
        analyzer = BYBPnLAnalyzer()

        if args.quick:
            # 快速分析模式
            logging.info("运行快速分析模式...")
            analyzer.get_current_assets()
            asset_pnl = analyzer.calculate_asset_pnl()

            if asset_pnl:
                print("\n" + "="*60)
                print("BYB资产快速分析".center(60))
                print("="*60)
                print(f"当前时间: {analyzer.current_time.strftime('%Y-%m-%d %H:%M:%S')}")
                print(f"BYB资产: {asset_pnl['byb_current']:,.2f} (变化: {asset_pnl['byb_change']:,.2f})")
                print(f"USDT资产: {asset_pnl['usdt_current']:,.2f} (变化: {asset_pnl['usdt_change']:,.2f})")
                print(f"当前BYB价格: {asset_pnl['current_price']:.6f} USDT")
                print(f"总盈亏: {asset_pnl['total_pnl_usdt']:,.2f} USDT")
                print("="*60)

                if args.lark:
                    msg = f"💰 BYB资产快速分析\n当前BYB: {asset_pnl['byb_current']:,.0f}\n当前USDT: {asset_pnl['usdt_current']:,.2f}\n总盈亏: {asset_pnl['total_pnl_usdt']:,.2f} USDT"
                    await send_lark(msg, level='info')
        else:
            # 完整分析模式
            result = await analyzer.run_full_analysis(
                send_to_lark=args.lark,
                create_charts=args.charts
            )

            if result:
                print(f"\n✅ 分析完成!")
                if result['csv_file']:
                    print(f"📄 CSV报告: {result['csv_file']}")
                if result['chart_file']:
                    print(f"📊 图表文件: {result['chart_file']}")

    except KeyboardInterrupt:
        logging.info("分析被用户中断")
    except Exception as e:
        logging.error(f"主函数执行失败: {str(e)}\n{traceback.format_exc()}")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logging.info("程序被用户中断")
    except Exception as e:
        logging.error(f"程序启动失败: {str(e)}\n{traceback.format_exc()}")
