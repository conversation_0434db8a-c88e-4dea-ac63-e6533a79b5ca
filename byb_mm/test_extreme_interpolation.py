#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试极端情况下的K线插值算法
专门针对长影线问题进行测试
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def create_extreme_gap_scenario(df):
    """
    创建极端间隙场景：在价格大幅跳空的地方创建间隙
    """
    df_extreme = df.copy()
    
    # 找到价格变化最大的地方
    price_changes = []
    for i in range(1, len(df)):
        prev_close = df['close'].iloc[i-1]
        curr_open = df['open'].iloc[i]
        change_pct = abs(curr_open - prev_close) / prev_close
        price_changes.append((i, change_pct))
    
    # 选择变化最大的10个位置
    price_changes.sort(key=lambda x: x[1], reverse=True)
    extreme_indices = [idx for idx, _ in price_changes[:10]]
    
    print(f"在以下位置创建极端间隙: {extreme_indices}")
    
    # 在这些位置创建连续的间隙（每个位置删除3-5根K线）
    gap_indices = []
    for idx in extreme_indices:
        gap_size = np.random.randint(3, 6)  # 3-5根K线的间隙
        for j in range(gap_size):
            if idx + j < len(df_extreme):
                gap_indices.append(idx + j)
                df_extreme.iloc[idx + j, df_extreme.columns.get_loc('high')] = np.nan
                df_extreme.iloc[idx + j, df_extreme.columns.get_loc('low')] = np.nan
                df_extreme.iloc[idx + j, df_extreme.columns.get_loc('open')] = np.nan
                df_extreme.iloc[idx + j, df_extreme.columns.get_loc('close')] = np.nan
    
    print(f"总共创建了 {len(gap_indices)} 个间隙点")
    return df_extreme, gap_indices

def ultra_conservative_interpolation(merged_df):
    """
    超保守插值方法：最小化影线长度
    """
    print("正在进行超保守插值...")
    
    # 首先对收盘价进行线性插值作为基准
    merged_df['close'] = merged_df['close'].interpolate(method='linear')
    merged_df['open'] = merged_df['open'].interpolate(method='linear')
    
    interpolated_count = 0
    for i in range(len(merged_df)):
        if pd.isna(merged_df['high'].iloc[i]) or pd.isna(merged_df['low'].iloc[i]):
            open_price = merged_df['open'].iloc[i]
            close_price = merged_df['close'].iloc[i]
            
            # 超保守策略：影线长度不超过实体的10%
            body_center = (open_price + close_price) / 2
            body_size = abs(close_price - open_price)
            
            # 最小实体大小
            min_body_size = body_center * 0.0005  # 0.05%
            effective_body_size = max(body_size, min_body_size)
            
            # 极小的影线长度
            max_shadow_length = effective_body_size * 0.1  # 仅10%
            
            body_high = max(open_price, close_price)
            body_low = min(open_price, close_price)
            
            # 设置极保守的高低价
            interpolated_high = body_high + max_shadow_length
            interpolated_low = body_low - max_shadow_length
            
            # 更严格的前后K线约束
            if i > 0 and not pd.isna(merged_df['high'].iloc[i-1]):
                prev_close = merged_df['close'].iloc[i-1]
                max_deviation = abs(prev_close) * 0.005  # 仅0.5%
                interpolated_high = min(interpolated_high, prev_close + max_deviation)
                interpolated_low = max(interpolated_low, prev_close - max_deviation)
            
            if i < len(merged_df) - 1 and not pd.isna(merged_df['high'].iloc[i+1]):
                next_close = merged_df['close'].iloc[i+1]
                max_deviation = abs(next_close) * 0.005  # 仅0.5%
                interpolated_high = min(interpolated_high, next_close + max_deviation)
                interpolated_low = max(interpolated_low, next_close - max_deviation)
            
            # 确保OHLC逻辑正确
            interpolated_high = max(interpolated_high, open_price, close_price)
            interpolated_low = min(interpolated_low, open_price, close_price)
            
            merged_df.iloc[i, merged_df.columns.get_loc('high')] = interpolated_high
            merged_df.iloc[i, merged_df.columns.get_loc('low')] = interpolated_low
            
            interpolated_count += 1
    
    print(f"超保守插值完成，共处理 {interpolated_count} 根K线")
    return merged_df

def analyze_extreme_cases(df, title=""):
    """
    专门分析极端影线情况
    """
    extreme_stats = {
        'shadow_ratios': [],
        'volatilities': [],
        'extreme_shadows': [],
        'extreme_volatilities': []
    }
    
    for i in range(len(df)):
        open_price = df['open'].iloc[i]
        close_price = df['close'].iloc[i]
        high_price = df['high'].iloc[i]
        low_price = df['low'].iloc[i]
        
        body_size = abs(close_price - open_price)
        body_high = max(open_price, close_price)
        body_low = min(open_price, close_price)
        
        upper_shadow = high_price - body_high
        lower_shadow = body_low - low_price
        
        # 影线比例
        if body_size > 0:
            upper_ratio = upper_shadow / body_size
            lower_ratio = lower_shadow / body_size
            max_ratio = max(upper_ratio, lower_ratio)
            extreme_stats['shadow_ratios'].append(max_ratio)
            
            # 记录极端影线（超过实体2倍）
            if max_ratio > 2.0:
                extreme_stats['extreme_shadows'].append(max_ratio)
        
        # 波动率
        if low_price > 0:
            volatility = high_price / low_price - 1
            extreme_stats['volatilities'].append(volatility)
            
            # 记录极端波动（超过2%）
            if volatility > 0.02:
                extreme_stats['extreme_volatilities'].append(volatility)
    
    # 统计分析
    total_klines = len(df)
    extreme_shadow_count = len(extreme_stats['extreme_shadows'])
    extreme_vol_count = len(extreme_stats['extreme_volatilities'])
    
    very_long_shadows = sum(1 for ratio in extreme_stats['shadow_ratios'] if ratio > 5.0)
    ultra_long_shadows = sum(1 for ratio in extreme_stats['shadow_ratios'] if ratio > 10.0)
    
    print(f"\n=== {title} 极端情况分析 ===")
    print(f"总K线数量: {total_klines}")
    print(f"影线超过实体2倍的K线: {extreme_shadow_count} ({extreme_shadow_count/total_klines*100:.1f}%)")
    print(f"影线超过实体5倍的K线: {very_long_shadows} ({very_long_shadows/total_klines*100:.1f}%)")
    print(f"影线超过实体10倍的K线: {ultra_long_shadows} ({ultra_long_shadows/total_klines*100:.1f}%)")
    print(f"波动率超过2%的K线: {extreme_vol_count} ({extreme_vol_count/total_klines*100:.1f}%)")
    
    if extreme_stats['shadow_ratios']:
        max_shadow_ratio = max(extreme_stats['shadow_ratios'])
        avg_shadow_ratio = np.mean(extreme_stats['shadow_ratios'])
        print(f"最大影线/实体比例: {max_shadow_ratio:.2f}")
        print(f"平均影线/实体比例: {avg_shadow_ratio:.2f}")
    
    if extreme_stats['volatilities']:
        max_volatility = max(extreme_stats['volatilities'])
        avg_volatility = np.mean(extreme_stats['volatilities'])
        print(f"最大波动率: {max_volatility:.4f} ({max_volatility*100:.2f}%)")
        print(f"平均波动率: {avg_volatility:.4f} ({avg_volatility*100:.2f}%)")
    
    return extreme_stats

def plot_extreme_comparison(df_original, df_traditional, df_conservative, gap_indices):
    """
    绘制极端情况对比图，重点展示问题区域
    """
    fig, axes = plt.subplots(3, 1, figsize=(20, 15), sharex=True)
    
    # 转换时间戳
    for df in [df_original, df_traditional, df_conservative]:
        df['datetime'] = pd.to_datetime(df['timestamp_sec'], unit='s')
    
    # 找到包含间隙的区域进行重点展示
    gap_start = min(gap_indices) - 10
    gap_end = max(gap_indices) + 10
    plot_range = slice(max(0, gap_start), min(len(df_original), gap_end))
    
    datasets = [
        (df_original.iloc[plot_range], "原始数据", axes[0]),
        (df_traditional.iloc[plot_range], "传统线性插值", axes[1]),
        (df_conservative.iloc[plot_range], "超保守插值", axes[2])
    ]
    
    for df_plot, title, ax in datasets:
        # 绘制K线
        for i in range(len(df_plot)):
            row = df_plot.iloc[i]
            x = row['datetime']
            open_price = row['open']
            high_price = row['high']
            low_price = row['low']
            close_price = row['close']
            
            # 计算影线长度比例
            body_size = abs(close_price - open_price)
            if body_size > 0:
                body_high = max(open_price, close_price)
                body_low = min(open_price, close_price)
                upper_shadow = high_price - body_high
                lower_shadow = body_low - low_price
                shadow_ratio = max(upper_shadow, lower_shadow) / body_size
                
                # 根据影线长度选择颜色
                if shadow_ratio > 5.0:
                    shadow_color = 'red'  # 极长影线
                elif shadow_ratio > 2.0:
                    shadow_color = 'orange'  # 长影线
                else:
                    shadow_color = 'black'  # 正常影线
            else:
                shadow_color = 'black'
            
            # 绘制影线
            ax.plot([x, x], [low_price, high_price], color=shadow_color, linewidth=1.2)
            
            # 绘制实体
            color = 'red' if close_price >= open_price else 'green'
            body_height = abs(close_price - open_price)
            body_bottom = min(open_price, close_price)
            
            if body_height > 0:
                from matplotlib.patches import Rectangle
                rect = Rectangle((mdates.date2num(x) - 0.0003, body_bottom),
                               0.0006, body_height,
                               facecolor=color, edgecolor='black', linewidth=0.5, alpha=0.8)
                ax.add_patch(rect)
        
        # 标记插值区域
        if title != "原始数据":
            gap_mask = [i for i in range(len(df_plot)) if (gap_start + i) in gap_indices]
            if gap_mask:
                gap_times = [df_plot.iloc[i]['datetime'] for i in gap_mask]
                gap_prices = [df_plot.iloc[i]['close'] for i in gap_mask]
                ax.scatter(gap_times, gap_prices, color='blue', s=30, alpha=0.8, 
                          label=f'插值点', zorder=5)
        
        ax.set_title(f"{title} - 极端间隙区域", fontsize=14, fontweight='bold')
        ax.set_ylabel('价格 (USDT)', fontsize=12)
        ax.grid(True, alpha=0.3)
        ax.legend()
    
    # 添加颜色说明
    from matplotlib.lines import Line2D
    legend_elements = [
        Line2D([0], [0], color='black', lw=2, label='正常影线'),
        Line2D([0], [0], color='orange', lw=2, label='长影线 (>2倍实体)'),
        Line2D([0], [0], color='red', lw=2, label='极长影线 (>5倍实体)')
    ]
    axes[0].legend(handles=legend_elements, loc='upper right')
    
    axes[-1].set_xlabel('时间', fontsize=12)
    axes[-1].xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
    
    plt.tight_layout()
    plt.savefig("extreme_interpolation_comparison.png", dpi=300, bbox_inches='tight')
    print("极端情况对比图已保存为: extreme_interpolation_comparison.png")
    plt.show()

def main():
    """
    主函数：测试极端情况下的插值算法
    """
    print("=== 极端K线插值算法测试 ===\n")
    
    # 读取原始数据
    df_original = pd.read_csv('bybusdt_1min_kline_2025-07-03.csv')
    print(f"原始数据: {len(df_original)} 根K线")
    
    # 分析原始数据
    original_stats = analyze_extreme_cases(df_original, "原始数据")
    
    # 创建极端间隙场景
    print("\n创建极端间隙场景...")
    df_extreme, gap_indices = create_extreme_gap_scenario(df_original)
    
    # 传统插值
    print("\n测试传统插值...")
    df_traditional = df_extreme.copy()
    for col in ['open', 'close', 'high', 'low']:
        df_traditional[col] = df_traditional[col].interpolate(method='linear')
    
    # 确保OHLC逻辑
    for i in range(len(df_traditional)):
        open_price = df_traditional['open'].iloc[i]
        close_price = df_traditional['close'].iloc[i]
        high_price = df_traditional['high'].iloc[i]
        low_price = df_traditional['low'].iloc[i]
        
        corrected_high = max(high_price, open_price, close_price)
        corrected_low = min(low_price, open_price, close_price)
        
        df_traditional.iloc[i, df_traditional.columns.get_loc('high')] = corrected_high
        df_traditional.iloc[i, df_traditional.columns.get_loc('low')] = corrected_low
    
    traditional_stats = analyze_extreme_cases(df_traditional, "传统插值")
    
    # 超保守插值
    print("\n测试超保守插值...")
    df_conservative = df_extreme.copy()
    df_conservative = ultra_conservative_interpolation(df_conservative)
    conservative_stats = analyze_extreme_cases(df_conservative, "超保守插值")
    
    # 对比分析
    print("\n=== 极端情况对比分析 ===")
    
    trad_extreme = sum(1 for ratio in traditional_stats['shadow_ratios'] if ratio > 2.0)
    cons_extreme = sum(1 for ratio in conservative_stats['shadow_ratios'] if ratio > 2.0)
    
    trad_ultra = sum(1 for ratio in traditional_stats['shadow_ratios'] if ratio > 10.0)
    cons_ultra = sum(1 for ratio in conservative_stats['shadow_ratios'] if ratio > 10.0)
    
    print(f"影线超过实体2倍的K线: {trad_extreme} → {cons_extreme} (减少 {trad_extreme - cons_extreme})")
    print(f"影线超过实体10倍的K线: {trad_ultra} → {cons_ultra} (减少 {trad_ultra - cons_ultra})")
    
    if traditional_stats['shadow_ratios'] and conservative_stats['shadow_ratios']:
        trad_max = max(traditional_stats['shadow_ratios'])
        cons_max = max(conservative_stats['shadow_ratios'])
        print(f"最大影线比例: {trad_max:.2f} → {cons_max:.2f}")
    
    # 绘制对比图
    print("\n生成极端情况对比图...")
    plot_extreme_comparison(df_original, df_traditional, df_conservative, gap_indices)
    
    print("\n=== 测试完成 ===")
    print("结论: 超保守插值算法显著减少了极端长影线的产生。")

if __name__ == "__main__":
    main()
