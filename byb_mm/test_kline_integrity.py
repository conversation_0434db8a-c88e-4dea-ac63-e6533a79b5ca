#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
K线完整性检查和智能填补功能测试脚本
测试新增的完整性检查和智能填补功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from kline_restore import check_kline_integrity, intelligent_fill_missing_bars, post_interpolation_shadow_check

def create_test_kline_data():
    """
    创建测试用的K线数据，包含一些缺失的时间点
    """
    print("创建测试K线数据...")
    
    # 创建基础时间序列（2025-07-03 09:00:00 开始的1分钟K线）
    start_time = datetime(2025, 7, 3, 9, 0, 0)
    base_price = 1.0
    
    # 创建20分钟的数据，但故意跳过几个时间点
    timestamps = []
    prices = []
    
    for i in range(20):
        # 故意跳过第5、10、15分钟，模拟缺失数据
        if i in [5, 10, 15]:
            continue
            
        current_time = start_time + timedelta(minutes=i)
        timestamps.append(int(current_time.timestamp()))
        
        # 生成模拟价格走势
        price_change = np.sin(i * 0.3) * 0.01 + np.random.normal(0, 0.002)
        current_price = base_price + price_change
        prices.append(current_price)
    
    # 创建OHLC数据
    test_data = []
    for i, (timestamp, price) in enumerate(zip(timestamps, prices)):
        # 生成OHLC价格
        volatility = 0.005  # 0.5%的波动
        
        open_price = price + np.random.normal(0, volatility * 0.5) * price
        close_price = price + np.random.normal(0, volatility * 0.5) * price
        
        # 生成高低价，有些K线故意设置过长的影线
        if i % 4 == 0:  # 每4根K线设置一个长影线
            high_price = max(open_price, close_price) * (1 + volatility * 3)  # 过长的上影线
            low_price = min(open_price, close_price) * (1 - volatility * 2)   # 过长的下影线
        else:
            high_price = max(open_price, close_price) * (1 + volatility * 0.5)
            low_price = min(open_price, close_price) * (1 - volatility * 0.5)
        
        # 生成交易量和交易额
        volume = 1000 + np.random.normal(0, 200)
        amount = volume * price
        
        test_data.append({
            'timestamp_sec': timestamp,
            'amount': round(amount, 6),
            'vol': round(volume, 6),
            'open': round(open_price, 6),
            'close': round(close_price, 6),
            'high': round(high_price, 6),
            'low': round(low_price, 6)
        })
    
    df = pd.DataFrame(test_data)
    print(f"测试数据创建完成，共{len(df)}条记录（应该缺失3条）")
    
    return df

def test_integrity_check():
    """测试完整性检查功能"""
    print("\n" + "="*60)
    print("测试K线完整性检查功能")
    print("="*60)
    
    # 创建测试数据
    df_test = create_test_kline_data()
    
    # 显示测试数据
    print("\n测试数据概览:")
    print(df_test[['timestamp_sec', 'open', 'close', 'high', 'low']].head(10))
    
    # 执行完整性检查
    print("\n执行完整性检查...")
    integrity_result = check_kline_integrity(df_test, timeframe_minutes=1)
    
    # 显示检查结果
    print(f"\n完整性检查结果:")
    print(f"  总K线数: {integrity_result['total_bars']}")
    print(f"  缺失K线数: {len(integrity_result['missing_bars'])}")
    print(f"  重复K线数: {len(integrity_result['duplicate_bars'])}")
    print(f"  不规律间隔数: {len(integrity_result['irregular_intervals'])}")
    print(f"  大时间间隔数: {len(integrity_result['time_gaps'])}")
    print(f"  数据质量评分: {integrity_result['data_quality_score']:.1f}/100")
    
    if integrity_result['missing_bars']:
        print(f"\n缺失的K线时间点:")
        for missing in integrity_result['missing_bars'][:5]:  # 只显示前5个
            missing_time = datetime.fromtimestamp(missing['timestamp_sec'])
            print(f"  - {missing_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    return df_test, integrity_result

def test_intelligent_fill():
    """测试智能填补功能"""
    print("\n" + "="*60)
    print("测试智能填补功能")
    print("="*60)
    
    # 获取测试数据和完整性检查结果
    df_test, integrity_result = test_integrity_check()
    
    if not integrity_result['missing_bars']:
        print("没有缺失的K线，无法测试填补功能")
        return df_test
    
    # 执行智能填补
    print(f"\n开始智能填补 {len(integrity_result['missing_bars'])} 个缺失K线...")
    df_filled = intelligent_fill_missing_bars(df_test, integrity_result, timeframe_minutes=1)
    
    print(f"\n填补结果:")
    print(f"  填补前K线数: {len(df_test)}")
    print(f"  填补后K线数: {len(df_filled)}")
    print(f"  新增K线数: {len(df_filled) - len(df_test)}")
    
    # 重新检查完整性
    print(f"\n重新检查完整性...")
    final_integrity = check_kline_integrity(df_filled, timeframe_minutes=1)
    print(f"  最终数据质量评分: {final_integrity['data_quality_score']:.1f}/100")
    print(f"  剩余缺失K线: {len(final_integrity['missing_bars'])}")
    
    return df_filled

def test_shadow_check():
    """测试插值后影线检查功能"""
    print("\n" + "="*60)
    print("测试插值后影线检查功能")
    print("="*60)
    
    # 获取填补后的数据
    df_filled = test_intelligent_fill()
    
    # 统计填补前的影线情况
    print(f"\n填补后影线检查前统计:")
    long_shadow_count = 0
    for i in range(len(df_filled)):
        open_price = df_filled['open'].iloc[i]
        close_price = df_filled['close'].iloc[i]
        high_price = df_filled['high'].iloc[i]
        low_price = df_filled['low'].iloc[i]
        
        body_size = abs(close_price - open_price)
        body_center = (open_price + close_price) / 2
        min_body_size = body_center * 0.001
        effective_body_size = max(body_size, min_body_size)
        
        upper_shadow = high_price - max(open_price, close_price)
        lower_shadow = min(open_price, close_price) - low_price
        
        if upper_shadow > effective_body_size or lower_shadow > effective_body_size:
            long_shadow_count += 1
    
    print(f"  过长影线K线数: {long_shadow_count}")
    
    # 执行影线检查和修复
    print(f"\n执行插值后影线检查...")
    df_shadow_fixed = post_interpolation_shadow_check(df_filled, max_shadow_ratio=1.0, min_body_ratio=0.001)
    
    # 统计修复后的影线情况
    print(f"\n影线修复后统计:")
    final_long_shadow_count = 0
    for i in range(len(df_shadow_fixed)):
        open_price = df_shadow_fixed['open'].iloc[i]
        close_price = df_shadow_fixed['close'].iloc[i]
        high_price = df_shadow_fixed['high'].iloc[i]
        low_price = df_shadow_fixed['low'].iloc[i]
        
        body_size = abs(close_price - open_price)
        body_center = (open_price + close_price) / 2
        min_body_size = body_center * 0.001
        effective_body_size = max(body_size, min_body_size)
        
        upper_shadow = high_price - max(open_price, close_price)
        lower_shadow = min(open_price, close_price) - low_price
        
        if upper_shadow > effective_body_size or lower_shadow > effective_body_size:
            final_long_shadow_count += 1
    
    print(f"  修复后过长影线K线数: {final_long_shadow_count}")
    print(f"  修复效果: 减少了 {long_shadow_count - final_long_shadow_count} 个过长影线")
    
    return df_shadow_fixed

def main():
    """主测试函数"""
    print("K线完整性检查和智能填补功能测试")
    print("="*80)
    
    try:
        # 测试完整性检查
        print("\n1. 测试完整性检查功能...")
        df_test, integrity_result = test_integrity_check()
        
        # 测试智能填补
        print("\n2. 测试智能填补功能...")
        df_filled = test_intelligent_fill()
        
        # 测试影线检查
        print("\n3. 测试插值后影线检查功能...")
        df_final = test_shadow_check()
        
        # 保存测试结果
        output_file = f'test_kline_integrity_result_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
        df_final.to_csv(output_file, index=False)
        print(f"\n测试结果已保存到: {output_file}")
        
        print("\n" + "="*80)
        print("✅ 所有测试完成！")
        print("新功能工作正常，可以集成到主程序中。")
        print("="*80)
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
