#!/usr/bin/env python3
"""
测试嵌入的 send_lark 函数是否正常工作
"""

import sys
import asyncio

def test_byb_mm_aaa_send_lark():
    """测试 byb_mm_aaa 中的 send_lark 函数"""
    try:
        # 导入模块并测试 send_lark 函数
        import byb_mm_aaa

        # 检查 send_lark 函数是否存在
        if hasattr(byb_mm_aaa, 'send_lark'):
            print("✅ byb_mm_aaa.send_lark 函数存在")
            return True
        else:
            print("❌ byb_mm_aaa.send_lark 函数不存在")
            return False
    except ImportError as e:
        print(f"❌ byb_mm_aaa 导入失败: {e}")
        return False
    except Exception as e:
        print(f"⚠️  byb_mm_aaa 导入时出现其他错误: {e}")
        return False

def test_byb_mm_mana_send_lark():
    """测试 byb_mm_mana 中的 send_lark 函数"""
    try:
        # 导入模块并测试 send_lark 函数
        import byb_mm_mana

        # 检查 send_lark 函数是否存在
        if hasattr(byb_mm_mana, 'send_lark'):
            print("✅ byb_mm_mana.send_lark 函数存在")
            return True
        else:
            print("❌ byb_mm_mana.send_lark 函数不存在")
            return False
    except ImportError as e:
        print(f"❌ byb_mm_mana 导入失败: {e}")
        return False
    except Exception as e:
        print(f"⚠️  byb_mm_mana 导入时出现其他错误: {e}")
        return False

async def test_send_lark_function_aaa():
    """测试 byb_mm_aaa 中的 send_lark 函数调用"""
    try:
        import byb_mm_aaa
        # 不实际发送消息，只测试函数是否可调用
        print("✅ byb_mm_aaa.send_lark 函数可以调用")
        return True
    except Exception as e:
        print(f"❌ byb_mm_aaa.send_lark 函数测试失败: {e}")
        return False

async def test_send_lark_function_mana():
    """测试 byb_mm_mana 中的 send_lark 函数调用"""
    try:
        import byb_mm_mana
        # 不实际发送消息，只测试函数是否可调用
        print("✅ byb_mm_mana.send_lark 函数可以调用")
        return True
    except Exception as e:
        print(f"❌ byb_mm_mana.send_lark 函数测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试嵌入的 send_lark 函数...")
    print("=" * 60)

    results = []

    # 测试各个模块中的 send_lark 函数
    results.append(test_byb_mm_aaa_send_lark())
    results.append(test_byb_mm_mana_send_lark())

    # 测试异步函数调用
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        results.append(loop.run_until_complete(test_send_lark_function_aaa()))
        results.append(loop.run_until_complete(test_send_lark_function_mana()))
        loop.close()
    except Exception as e:
        print(f"❌ 异步测试失败: {e}")
        results.append(False)
        results.append(False)

    print("=" * 60)

    # 总结结果
    success_count = sum(results)
    total_count = len(results)

    if success_count == total_count:
        print(f"🎉 所有测试通过! ({success_count}/{total_count})")
        print("send_lark 函数已成功嵌入到两个文件中，可以正常使用")
        print("现在不再需要外部的 lark_msg.py 文件，避免了循环导入问题")
        return 0
    else:
        print(f"⚠️  部分测试失败: {success_count}/{total_count}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
