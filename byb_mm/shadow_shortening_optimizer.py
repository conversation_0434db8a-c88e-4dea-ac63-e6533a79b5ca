import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def shorten_long_shadows(df, max_shadow_ratio=1.0, min_body_ratio=0.001):
    """
    缩短K线的长影线
    Args:
        df: K线数据DataFrame
        max_shadow_ratio: 影线长度相对于实体的最大比例 (默认1倍，即影线≤实体)
        min_body_ratio: 最小实体大小相对于价格的比例 (默认0.1%)
    Returns:
        优化后的DataFrame
    """
    df_optimized = df.copy()
    shadow_shortened_count = 0
    
    print(f"开始缩短长影线，影线限制为实体的{max_shadow_ratio}倍（影线≤实体）...")
    
    for i in range(len(df_optimized)):
        open_price = df_optimized['open'].iloc[i]
        close_price = df_optimized['close'].iloc[i]
        high_price = df_optimized['high'].iloc[i]
        low_price = df_optimized['low'].iloc[i]
        
        # 计算实体大小和中心价格
        body_size = abs(close_price - open_price)
        body_center = (open_price + close_price) / 2
        
        # 如果实体很小，使用最小实体大小来计算影线限制
        min_body_size = body_center * min_body_ratio
        effective_body_size = max(body_size, min_body_size)
        
        # 影线长度限制
        max_shadow_length = effective_body_size * max_shadow_ratio
        
        # 原始影线长度
        upper_shadow = high_price - max(open_price, close_price)
        lower_shadow = min(open_price, close_price) - low_price
        
        # 缩短上影线
        max_allowed_high = max(open_price, close_price) + max_shadow_length
        new_high = high_price
        if high_price > max_allowed_high:
            new_high = max_allowed_high
            shadow_shortened_count += 1
        
        # 缩短下影线
        min_allowed_low = min(open_price, close_price) - max_shadow_length
        new_low = low_price
        if low_price < min_allowed_low:
            new_low = min_allowed_low
            shadow_shortened_count += 1
        
        # 应用调整
        df_optimized.iloc[i, df_optimized.columns.get_loc('high')] = new_high
        df_optimized.iloc[i, df_optimized.columns.get_loc('low')] = new_low
        
        # 记录显著的影线缩短
        if upper_shadow > max_shadow_length * 1.2 or lower_shadow > max_shadow_length * 1.2:
            timestamp = datetime.fromtimestamp(df_optimized['timestamp_sec'].iloc[i])
            print(f"  缩短长影线 - 时间: {timestamp}")
            if upper_shadow > max_shadow_length * 1.2:
                print(f"    上影线: {high_price:.6f} → {new_high:.6f} (缩短 {high_price - new_high:.6f})")
            if lower_shadow > max_shadow_length * 1.2:
                print(f"    下影线: {low_price:.6f} → {new_low:.6f} (缩短 {new_low - low_price:.6f})")
    
    print(f"长影线缩短完成，共处理 {shadow_shortened_count} 个影线")
    
    return df_optimized

def analyze_shadow_lengths(df, title="影线长度分析"):
    """分析K线的影线长度分布"""
    
    upper_shadows = []
    lower_shadows = []
    body_sizes = []
    shadow_ratios = []
    
    for i in range(len(df)):
        open_price = df['open'].iloc[i]
        close_price = df['close'].iloc[i]
        high_price = df['high'].iloc[i]
        low_price = df['low'].iloc[i]
        
        body_size = abs(close_price - open_price)
        body_center = (open_price + close_price) / 2
        
        upper_shadow = high_price - max(open_price, close_price)
        lower_shadow = min(open_price, close_price) - low_price
        
        upper_shadows.append(upper_shadow)
        lower_shadows.append(lower_shadow)
        body_sizes.append(body_size)
        
        # 计算影线与实体的比例
        if body_size > 0:
            max_shadow = max(upper_shadow, lower_shadow)
            shadow_ratios.append(max_shadow / body_size)
        else:
            # 十字星情况，使用价格的0.2%作为基准
            min_body = body_center * 0.002
            max_shadow = max(upper_shadow, lower_shadow)
            shadow_ratios.append(max_shadow / min_body)
    
    print(f"\n=== {title} ===")
    print(f"上影线统计:")
    print(f"  平均长度: {np.mean(upper_shadows):.6f}")
    print(f"  最大长度: {max(upper_shadows):.6f}")
    print(f"  超过实体1倍的数量: {sum(1 for i, us in enumerate(upper_shadows) if us > body_sizes[i] * 1)}")

    print(f"下影线统计:")
    print(f"  平均长度: {np.mean(lower_shadows):.6f}")
    print(f"  最大长度: {max(lower_shadows):.6f}")
    print(f"  超过实体1倍的数量: {sum(1 for i, ls in enumerate(lower_shadows) if ls > body_sizes[i] * 1)}")

    print(f"影线/实体比例统计:")
    print(f"  平均比例: {np.mean(shadow_ratios):.2f}")
    print(f"  最大比例: {max(shadow_ratios):.2f}")
    print(f"  超过1倍的K线数: {sum(1 for ratio in shadow_ratios if ratio > 1)}")
    
    return {
        'upper_shadows': upper_shadows,
        'lower_shadows': lower_shadows,
        'body_sizes': body_sizes,
        'shadow_ratios': shadow_ratios
    }

def plot_shadow_comparison(df_original, df_optimized):
    """绘制影线优化前后的对比图"""
    
    # 转换时间
    df_original['datetime'] = pd.to_datetime(df_original['timestamp_sec'], unit='s')
    df_optimized['datetime'] = pd.to_datetime(df_optimized['timestamp_sec'], unit='s')
    
    fig, axes = plt.subplots(3, 1, figsize=(18, 14), 
                            gridspec_kw={'height_ratios': [2, 2, 1]})
    
    # 1. 原始K线图
    plot_candlestick_with_shadows(axes[0], df_original, "原始K线图 (优化前)", 'red')
    
    # 2. 优化后K线图
    plot_candlestick_with_shadows(axes[1], df_optimized, "优化后K线图 (长影线已缩短)", 'blue')
    
    # 3. 影线长度对比
    original_stats = analyze_shadow_lengths(df_original, "原始数据")
    optimized_stats = analyze_shadow_lengths(df_optimized, "优化后数据")
    
    # 绘制影线比例对比
    axes[2].plot(df_original['datetime'], original_stats['shadow_ratios'], 
                label='原始影线/实体比例', color='red', alpha=0.7, linewidth=1)
    axes[2].plot(df_optimized['datetime'], optimized_stats['shadow_ratios'], 
                label='优化后影线/实体比例', color='blue', linewidth=2)
    axes[2].axhline(y=1, color='orange', linestyle='--', alpha=0.8, label='1倍限制线')

    axes[2].set_title('影线/实体比例对比', fontsize=14, fontweight='bold')
    axes[2].set_ylabel('比例')
    axes[2].set_xlabel('时间')
    axes[2].legend()
    axes[2].grid(True, alpha=0.3)
    axes[2].set_ylim(0, min(max(original_stats['shadow_ratios']), 10))
    
    # 设置x轴格式
    for ax in axes:
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
        ax.xaxis.set_major_locator(mdates.HourLocator(interval=2))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
    
    plt.tight_layout()
    
    # 保存图表
    filename = f'bybusdt_shadow_optimization_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"影线优化对比图已保存为: {filename}")
    
    plt.show()
    return filename

def plot_candlestick_with_shadows(ax, df, title, color_theme):
    """绘制带影线强调的K线图"""
    
    for i in range(len(df)):
        row = df.iloc[i]
        x = row['datetime']
        open_price = row['open']
        high_price = row['high']
        low_price = row['low']
        close_price = row['close']
        
        # 确定颜色
        if color_theme == 'red':
            up_color = 'red'
            down_color = 'green'
            shadow_color = 'darkred'
        else:
            up_color = 'darkblue'
            down_color = 'lightblue'
            shadow_color = 'navy'
        
        color = up_color if close_price >= open_price else down_color
        
        # 绘制影线 (加粗显示)
        ax.plot([x, x], [low_price, high_price], color=shadow_color, linewidth=1.2, alpha=0.8)
        
        # 绘制实体
        body_height = abs(close_price - open_price)
        body_bottom = min(open_price, close_price)
        
        if body_height > 0:
            rect = Rectangle((mdates.date2num(x) - 0.0003, body_bottom), 
                           0.0006, body_height, 
                           facecolor=color, edgecolor='black', linewidth=0.5, alpha=0.8)
            ax.add_patch(rect)
        else:
            # 十字星
            ax.plot([mdates.date2num(x) - 0.0003, mdates.date2num(x) + 0.0003], 
                    [open_price, open_price], color='black', linewidth=1.5)
    
    # 设置图表
    ax.set_title(title, fontsize=14, fontweight='bold')
    ax.set_ylabel('价格 (USDT)')
    ax.grid(True, alpha=0.3)
    
    # 添加统计信息
    stats_text = f"开盘: {df['open'].iloc[0]:.6f} | 收盘: {df['close'].iloc[-1]:.6f} | " \
                f"最高: {df['high'].max():.6f} | 最低: {df['low'].min():.6f}"
    
    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.7),
            verticalalignment='top', fontsize=10)

def main():
    """主函数"""
    try:
        # 读取K线数据
        print("正在读取K线数据...")
        
        # 尝试读取不同的数据文件
        input_files = [
            'bybusdt_1min_kline_ultra_optimized.csv',
            'bybusdt_1min_kline_2025-07-03.csv'
        ]
        
        df = None
        input_file = None
        for file in input_files:
            try:
                df = pd.read_csv(file)
                input_file = file
                print(f"成功读取文件: {file}")
                break
            except FileNotFoundError:
                continue
        
        if df is None:
            print("错误: 未找到任何K线数据文件")
            return
        
        print(f"读取到 {len(df)} 条K线数据")
        
        # 分析原始影线长度
        print("\n分析原始数据的影线长度...")
        original_stats = analyze_shadow_lengths(df, "原始数据")
        
        # 应用影线缩短优化
        print("\n开始缩短长影线...")
        df_optimized = shorten_long_shadows(df, max_shadow_ratio=1.0, min_body_ratio=0.001)
        
        # 分析优化后的影线长度
        print("\n分析优化后数据的影线长度...")
        optimized_stats = analyze_shadow_lengths(df_optimized, "优化后数据")
        
        # 绘制对比图
        print("\n正在生成影线优化对比图...")
        chart_file = plot_shadow_comparison(df, df_optimized)
        
        # 保存优化后的数据
        output_file = 'bybusdt_1min_kline_shadow_optimized.csv'
        df_optimized.to_csv(output_file, index=False)
        print(f"\n影线优化后的K线数据已保存为: {output_file}")
        
        # 优化效果总结
        print(f"\n=== 影线优化效果总结 ===")
        original_long_shadows = sum(1 for ratio in original_stats['shadow_ratios'] if ratio > 1)
        optimized_long_shadows = sum(1 for ratio in optimized_stats['shadow_ratios'] if ratio > 1)

        print(f"超过1倍实体的长影线K线:")
        print(f"  优化前: {original_long_shadows} 根")
        print(f"  优化后: {optimized_long_shadows} 根")
        print(f"  减少: {original_long_shadows - optimized_long_shadows} 根")
        
        avg_ratio_reduction = np.mean(original_stats['shadow_ratios']) - np.mean(optimized_stats['shadow_ratios'])
        max_ratio_reduction = max(original_stats['shadow_ratios']) - max(optimized_stats['shadow_ratios'])
        
        print(f"影线/实体比例:")
        print(f"  平均比例降低: {avg_ratio_reduction:.2f}")
        print(f"  最大比例降低: {max_ratio_reduction:.2f}")
        
        if optimized_long_shadows == 0:
            print("✅ 成功！所有K线的影线都已控制在实体长度以内")
        else:
            print(f"⚠ 仍有{optimized_long_shadows}根K线的影线超过实体长度")
        
        print(f"\n=== 文件生成完成 ===")
        print(f"影线优化对比图: {chart_file}")
        print(f"影线优化后数据: {output_file}")
        
    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
