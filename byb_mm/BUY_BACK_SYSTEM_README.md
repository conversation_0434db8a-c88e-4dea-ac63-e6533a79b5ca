# BYB智能回购系统

## 概述

BYB智能回购系统是一个自动化交易系统，能够监控数据库参数变化，自动执行下单算法，支持参数更新、余额计算和动态调整。该系统集成了原有的下单算法，提供了完整的参数监控和执行管理功能。

## 主要功能

### 🎯 核心功能
- **参数监控**: 实时监控数据库 `buy_back_params` 表的参数变化
- **动态调整**: 参数变化时自动停止当前算法，启动新的下单计划
- **余额管理**: 自动查询账户余额，计算剩余需购买金额
- **状态持久化**: 保存执行状态，支持程序重启后恢复
- **智能下单**: 集成随机分布下单算法，优化执行策略
- **实时通知**: Lark通知系统，及时推送执行状态

### 📊 系统特性
- **自动化执行**: 无需人工干预，全自动监控和执行
- **参数响应**: 数据库参数变化后30秒内响应
- **余额保护**: 自动检查USDT余额，避免资金不足
- **状态恢复**: 程序重启后自动恢复执行状态
- **异常处理**: 完善的错误处理和恢复机制

## 文件结构

```
byb_mm/
├── byb_buy_back.py              # 主系统文件（集成版）
├── byb_order_algorithm.py       # 下单算法模块
├── test_buy_back_system.py      # 系统测试脚本
├── BUY_BACK_SYSTEM_README.md    # 使用说明（本文件）
├── byb_buy_back_state.json      # 状态文件（自动生成）
└── byb_buy_back_system.log      # 系统日志（自动生成）
```

## 数据库配置

### 参数表结构
系统监控 `buy_back_params` 表：

```sql
CREATE TABLE buy_back_params (
    id INT PRIMARY KEY AUTO_INCREMENT,
    days DECIMAL(10,2) NOT NULL,        -- 执行天数
    total_amount DECIMAL(15,2) NOT NULL, -- 总下单量（BYB）
    each_amount DECIMAL(15,2),           -- 单次下单量参考
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 参数变化监控
系统监控以下字段的变化：
- `days`: 执行天数
- `total_amount`: 总下单量
- `each_amount`: 单次下单量参考

任一字段变化都会触发新的下单计划。

## 系统配置

### 数据库配置
```python
DB_CONFIG = {
    "host": "127.0.0.1",
    "port": 3306,
    "user": "weili",
    "password": "12345678",
    "database": "buy_back"
}
```

### 监控配置
```python
MONITOR_CONFIG = {
    "check_interval": 30,    # 参数检查间隔（秒）
    "symbol": "bybusdt",     # 交易对
    "base_currency": "byb",  # 基础货币
    "quote_currency": "usdt" # 计价货币
}
```

## 使用方法

### 1. 基本使用

```bash
# 运行回购系统
cd /path/to/byb_mm
python byb_buy_back.py
```

### 2. 测试系统

```bash
# 运行测试套件
python test_buy_back_system.py
```

### 3. 程序化使用

```python
import asyncio
from byb_buy_back import BYBBuyBackSystem

async def main():
    # 创建系统实例
    system = BYBBuyBackSystem()
    
    # 运行监控系统
    await system.monitor_and_execute()

# 运行
asyncio.run(main())
```

## 系统流程

### 1. 初始化阶段
1. 加载历史状态（已花费金额、执行记录）
2. 初始化交易客户端和数据库连接
3. 发送启动通知到Lark

### 2. 监控循环
1. 每30秒检查数据库参数是否变化
2. 参数变化时触发处理流程
3. 无变化时继续监控

### 3. 参数变化处理
1. 停止当前运行的下单算法
2. 更新已花费USDT金额
3. 查询当前账户余额
4. 计算剩余需购买金额
5. 检查USDT余额是否充足
6. 启动新的下单算法

### 4. 下单执行
1. 使用修改后的参数（剩余金额）
2. 调用集成的下单算法
3. 记录执行结果
4. 更新系统状态

## 状态管理

### 状态文件
系统自动维护 `byb_buy_back_state.json` 文件：

```json
{
  "total_spent_usdt": 1500.50,
  "execution_history": [
    {
      "start_time": "2025-07-07T10:00:00+08:00",
      "params": {...},
      "remaining_amount": 5000.0,
      "status": "completed",
      "orders_executed": 25,
      "total_executed": 4800.0
    }
  ],
  "last_update": "2025-07-07T15:30:00+08:00"
}
```

### 状态恢复
- 程序重启后自动加载历史状态
- 继续监控参数变化
- 保持执行连续性

## 监控和通知

### Lark通知类型
- **启动通知**: 系统启动时发送基本信息
- **参数变化通知**: 检测到参数变化时发送详细信息
- **余额预警**: USDT余额不足时发送预警
- **完成通知**: 回购任务完成时发送统计信息
- **异常通知**: 系统异常时发送错误信息

### 日志记录
- `byb_buy_back_system.log`: 详细的系统运行日志
- 包含参数变化、执行状态、异常信息等

## 余额管理

### 自动余额检查
- 每次参数变化时查询账户余额
- 检查USDT余额是否足够执行剩余购买
- 余额不足时发送预警通知

### 已花费金额计算
- 基于执行历史计算已花费USDT
- 支持手动更新和自动计算
- 用于计算剩余需购买金额

## 安全特性

### 风险控制
- **余额保护**: 自动检查资金充足性
- **参数验证**: 验证数据库参数合理性
- **执行限制**: 继承下单算法的安全限制
- **状态保存**: 实时保存执行状态

### 异常处理
- **网络异常**: 自动重试和恢复
- **数据库异常**: 错误处理和通知
- **API异常**: 完善的错误处理机制
- **程序异常**: 优雅退出和状态保存

## 性能优化

### 监控效率
- **轮询间隔**: 30秒检查一次，平衡响应性和资源消耗
- **数据库连接**: 按需连接，及时释放
- **内存管理**: 合理管理状态数据

### 执行优化
- **算法集成**: 复用现有下单算法
- **并发处理**: 异步执行，提高效率
- **状态缓存**: 减少重复计算

## 故障排除

### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查数据库配置
   python -c "from byb_buy_back import BYBBuyBackSystem; print(BYBBuyBackSystem().get_buy_back_params())"
   ```

2. **余额获取失败**
   ```bash
   # 检查API配置
   python -c "from byb_buy_back import BYBBuyBackSystem; print(BYBBuyBackSystem().get_account_balance())"
   ```

3. **参数监控异常**
   ```bash
   # 查看系统日志
   tail -f byb_buy_back_system.log
   ```

### 调试方法

```bash
# 运行完整测试
python test_buy_back_system.py

# 检查系统状态
python -c "
from byb_buy_back import BYBBuyBackSystem
system = BYBBuyBackSystem()
print(system.get_system_status())
"

# 查看状态文件
cat byb_buy_back_state.json
```

## 使用示例

### 场景1：首次启动
1. 设置数据库参数：7天，50000 BYB
2. 启动系统：`python byb_buy_back.py`
3. 系统开始执行下单算法

### 场景2：参数调整
1. 修改数据库参数：改为10天，80000 BYB
2. 系统自动检测变化（30秒内）
3. 停止当前算法，计算剩余金额
4. 启动新的下单计划

### 场景3：程序重启
1. 程序意外停止
2. 重新启动：`python byb_buy_back.py`
3. 自动加载历史状态
4. 继续监控参数变化

## 注意事项

1. **资金安全**: 确保USDT余额充足
2. **参数合理**: 设置合理的天数和金额
3. **网络稳定**: 确保网络连接稳定
4. **监控运行**: 定期检查系统状态
5. **备份状态**: 定期备份状态文件

## 更新日志

### v1.0.0 (2025-07-07)
- 初始版本发布
- 集成下单算法
- 实现参数监控功能
- 添加余额管理
- 完善状态持久化
- 集成Lark通知系统
