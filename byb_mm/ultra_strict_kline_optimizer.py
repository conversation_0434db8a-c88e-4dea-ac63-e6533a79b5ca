import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def ultra_strict_ohlc_optimization(df, max_volatility=0.01):
    """
    超严格的OHLC优化，确保每根K线波动(high/low-1)不超过1%
    Args:
        df: K线数据DataFrame
        max_volatility: 最大允许波动率，使用high/low-1公式 (默认1%)
    Returns:
        优化后的DataFrame
    """
    df_optimized = df.copy()
    
    print(f"开始超严格OHLC优化，确保所有K线波动(high/low-1)≤{max_volatility*100:.1f}%...")
    
    # 第一步：强力平滑收盘价序列
    smoothing_factor = 0.8  # 非常强的平滑
    smoothed_close = [df_optimized['close'].iloc[0]]
    
    for i in range(1, len(df_optimized)):
        prev_close = smoothed_close[-1]
        current_close = df_optimized['close'].iloc[i]
        
        # 限制收盘价变化幅度为0.5%
        max_change = prev_close * 0.005
        if abs(current_close - prev_close) > max_change:
            if current_close > prev_close:
                new_close = prev_close + max_change
            else:
                new_close = prev_close - max_change
        else:
            # 应用强平滑
            new_close = smoothing_factor * prev_close + (1 - smoothing_factor) * current_close
        
        smoothed_close.append(new_close)
    
    df_optimized['close'] = smoothed_close
    
    # 第二步：基于平滑收盘价调整开盘价
    df_optimized.iloc[0, df_optimized.columns.get_loc('open')] = df_optimized['close'].iloc[0]
    
    for i in range(1, len(df_optimized)):
        prev_close = df_optimized['close'].iloc[i-1]
        current_open = df_optimized['open'].iloc[i]
        
        # 限制开盘价跳空为0.3%
        max_gap = prev_close * 0.003
        if abs(current_open - prev_close) > max_gap:
            if current_open > prev_close:
                new_open = prev_close + max_gap
            else:
                new_open = prev_close - max_gap
            df_optimized.iloc[i, df_optimized.columns.get_loc('open')] = new_open
    
    # 第三步：严格控制每根K线的高低价，确保high/low-1≤max_volatility
    optimization_count = 0
    
    for i in range(len(df_optimized)):
        open_price = df_optimized['open'].iloc[i]
        close_price = df_optimized['close'].iloc[i]
        high_price = df_optimized['high'].iloc[i]
        low_price = df_optimized['low'].iloc[i]
        
        # 确保高低价包含开盘价和收盘价
        min_high = max(open_price, close_price)
        max_low = min(open_price, close_price)
        
        # 计算当前波动率 (high/low - 1)
        if low_price > 0:
            current_volatility = high_price / low_price - 1
        else:
            current_volatility = 0
        
        if current_volatility > max_volatility:
            # 严格按照high/low-1公式调整
            # 目标：high = low * (1 + max_volatility)
            
            # 策略1：以低价为基准
            target_high_from_low = max_low * (1 + max_volatility)
            
            # 策略2：以高价为基准
            target_low_from_high = min_high / (1 + max_volatility)
            
            # 选择最保守的策略
            if target_high_from_low >= min_high and target_low_from_high <= max_low:
                # 两种策略都可行，选择变化最小的
                change1 = abs(target_high_from_low - high_price) + abs(max_low - low_price)
                change2 = abs(min_high - high_price) + abs(target_low_from_high - low_price)
                
                if change1 <= change2:
                    new_high = target_high_from_low
                    new_low = max_low
                else:
                    new_high = min_high
                    new_low = target_low_from_high
            elif target_high_from_low >= min_high:
                # 策略1可行
                new_high = target_high_from_low
                new_low = max_low
            elif target_low_from_high <= max_low:
                # 策略2可行
                new_high = min_high
                new_low = target_low_from_high
            else:
                # 两种策略都不可行，强制调整
                # 在开盘收盘价范围内，尽可能满足波动率要求
                oc_mid = (open_price + close_price) / 2
                
                # 尝试以中点为基准
                test_low = oc_mid / (1 + max_volatility/2)
                test_high = test_low * (1 + max_volatility)
                
                if test_high >= min_high and test_low <= max_low:
                    new_high = test_high
                    new_low = test_low
                else:
                    # 最后手段：强制设置为开盘收盘价
                    new_high = min_high
                    new_low = max_low
            
            # 应用调整
            df_optimized.iloc[i, df_optimized.columns.get_loc('high')] = new_high
            df_optimized.iloc[i, df_optimized.columns.get_loc('low')] = new_low
            optimization_count += 1
    
    # 第四步：最终验证和微调
    final_violations = 0
    for i in range(len(df_optimized)):
        high_price = df_optimized['high'].iloc[i]
        low_price = df_optimized['low'].iloc[i]
        
        if low_price > 0:
            volatility = high_price / low_price - 1
            if volatility > max_volatility * 1.001:  # 允许0.1%的误差
                # 强制调整到精确限制
                new_high = low_price * (1 + max_volatility)
                df_optimized.iloc[i, df_optimized.columns.get_loc('high')] = new_high
                final_violations += 1
    
    # 统计优化效果
    original_volatilities = []
    optimized_volatilities = []
    over_limit_count = 0
    
    for i in range(len(df)):
        # 原始波动率
        if df['low'].iloc[i] > 0:
            orig_vol = df['high'].iloc[i] / df['low'].iloc[i] - 1
            original_volatilities.append(orig_vol)
        
        # 优化后波动率
        if df_optimized['low'].iloc[i] > 0:
            opt_vol = df_optimized['high'].iloc[i] / df_optimized['low'].iloc[i] - 1
            optimized_volatilities.append(opt_vol)
            
            if opt_vol > max_volatility:
                over_limit_count += 1
    
    avg_original_vol = np.mean(original_volatilities) if original_volatilities else 0
    avg_optimized_vol = np.mean(optimized_volatilities) if optimized_volatilities else 0
    max_original_vol = max(original_volatilities) if original_volatilities else 0
    max_optimized_vol = max(optimized_volatilities) if optimized_volatilities else 0
    
    original_over_limit = sum(1 for vol in original_volatilities if vol > max_volatility)
    
    print(f"超严格OHLC优化完成:")
    print(f"  优化操作次数: {optimization_count}")
    print(f"  最终微调次数: {final_violations}")
    print(f"  平均波动率: {avg_original_vol:.4f} → {avg_optimized_vol:.4f}")
    print(f"  最大波动率: {max_original_vol:.4f} → {max_optimized_vol:.4f}")
    print(f"  超过{max_volatility*100:.1f}%限制的K线: {original_over_limit} → {over_limit_count}")
    
    if over_limit_count == 0:
        print(f"✅ 所有K线波动(high/low-1)已严格控制在{max_volatility*100:.1f}%以内")
    else:
        print(f"⚠ 仍有{over_limit_count}根K线超过限制")
        
        # 显示超限的K线详情
        print("超限K线详情:")
        for i, vol in enumerate(optimized_volatilities):
            if vol > max_volatility:
                timestamp = datetime.fromtimestamp(df_optimized['timestamp_sec'].iloc[i])
                print(f"  {timestamp}: {vol:.4f} ({vol*100:.2f}%)")
    
    return df_optimized

def main():
    """主函数"""
    try:
        # 读取原始K线数据
        print("正在读取原始K线数据...")
        df = pd.read_csv('bybusdt_1min_kline_2025-07-03.csv')
        print(f"读取到 {len(df)} 条K线数据")
        
        # 应用超严格优化 (设为0.99%确保严格小于1%)
        df_optimized = ultra_strict_ohlc_optimization(df, max_volatility=0.0099)
        
        # 保存优化后的数据
        output_file = 'bybusdt_1min_kline_ultra_optimized.csv'
        df_optimized.to_csv(output_file, index=False)
        print(f"\n超严格优化后的K线数据已保存为: {output_file}")
        
        # 显示前10条数据
        print("\n前10条优化后的K线数据:")
        print(df_optimized[['timestamp_sec', 'open', 'close', 'high', 'low']].head(10).to_string(index=False))
        
        # 最终验证
        print(f"\n=== 最终验证 ===")
        violation_count = 0
        max_actual_vol = 0
        violation_details = []
        
        for i in range(len(df_optimized)):
            if df_optimized['low'].iloc[i] > 0:
                volatility = df_optimized['high'].iloc[i] / df_optimized['low'].iloc[i] - 1
                max_actual_vol = max(max_actual_vol, volatility)
                
                if volatility >= 0.01:  # 大于等于1%算超限
                    violation_count += 1
                    timestamp = datetime.fromtimestamp(df_optimized['timestamp_sec'].iloc[i])
                    violation_details.append((timestamp, volatility))
        
        print(f"最大实际波动率: {max_actual_vol:.4f} ({max_actual_vol*100:.2f}%)")
        print(f"超过1%限制的K线数: {violation_count}")
        
        if violation_count == 0:
            print("✅ 成功！所有K线波动(high/low-1)都严格在1%以内")
        else:
            print(f"❌ 仍有{violation_count}根K线超过1%限制")
            print("详细信息:")
            for timestamp, vol in violation_details[:10]:  # 只显示前10个
                print(f"  {timestamp}: {vol:.4f} ({vol*100:.2f}%)")
        
    except FileNotFoundError:
        print("错误: 未找到文件 'bybusdt_1min_kline_2025-07-03.csv'")
        print("请先运行 kline_restore.py 生成原始K线数据")
    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
