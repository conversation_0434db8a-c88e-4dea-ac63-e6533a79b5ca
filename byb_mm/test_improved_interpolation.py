#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进的K线插值算法
分析插值前后影线长度的变化
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def smart_kline_interpolation(merged_df):
    """
    智能K线插值，保持OHLC逻辑关系和影线合理性
    Args:
        merged_df: 包含缺失数据的DataFrame
    Returns:
        插值后的DataFrame
    """
    print("正在进行智能K线插值...")
    
    # 首先对收盘价进行线性插值作为基准
    merged_df['close'] = merged_df['close'].interpolate(method='linear')
    
    # 对开盘价进行插值，但限制与前一根K线收盘价的跳空幅度
    merged_df['open'] = merged_df['open'].interpolate(method='linear')
    
    # 对每个缺失的K线进行OHLC修正
    interpolated_count = 0
    for i in range(len(merged_df)):
        if pd.isna(merged_df['high'].iloc[i]) or pd.isna(merged_df['low'].iloc[i]):
            # 这是一个插值K线
            open_price = merged_df['open'].iloc[i]
            close_price = merged_df['close'].iloc[i]
            
            # 计算合理的高低价范围
            body_center = (open_price + close_price) / 2
            body_size = abs(close_price - open_price)
            
            # 如果实体很小，使用最小实体大小
            min_body_size = body_center * 0.001  # 0.1%
            effective_body_size = max(body_size, min_body_size)
            
            # 限制影线长度不超过实体的50%（比正常K线更保守）
            max_shadow_length = effective_body_size * 0.5
            
            # 计算高低价
            body_high = max(open_price, close_price)
            body_low = min(open_price, close_price)
            
            # 设置合理的高低价
            interpolated_high = body_high + max_shadow_length
            interpolated_low = body_low - max_shadow_length
            
            # 考虑前后K线的价格范围，避免过度偏离
            if i > 0 and not pd.isna(merged_df['high'].iloc[i-1]):
                prev_high = merged_df['high'].iloc[i-1]
                prev_low = merged_df['low'].iloc[i-1]
                
                # 限制插值K线的价格不要偏离前一根K线太远
                max_deviation = abs(merged_df['close'].iloc[i-1]) * 0.02  # 2%
                interpolated_high = min(interpolated_high, prev_high + max_deviation)
                interpolated_low = max(interpolated_low, prev_low - max_deviation)
            
            if i < len(merged_df) - 1 and not pd.isna(merged_df['high'].iloc[i+1]):
                next_high = merged_df['high'].iloc[i+1]
                next_low = merged_df['low'].iloc[i+1]
                
                # 限制插值K线的价格不要偏离后一根K线太远
                max_deviation = abs(merged_df['close'].iloc[i+1]) * 0.02  # 2%
                interpolated_high = min(interpolated_high, next_high + max_deviation)
                interpolated_low = max(interpolated_low, next_low - max_deviation)
            
            # 确保OHLC逻辑正确
            interpolated_high = max(interpolated_high, open_price, close_price)
            interpolated_low = min(interpolated_low, open_price, close_price)
            
            # 应用插值结果
            merged_df.iloc[i, merged_df.columns.get_loc('high')] = interpolated_high
            merged_df.iloc[i, merged_df.columns.get_loc('low')] = interpolated_low
            
            interpolated_count += 1
    
    print(f"智能插值完成，共处理 {interpolated_count} 根K线，影线长度已优化")
    return merged_df

def traditional_interpolation(merged_df):
    """
    传统线性插值方法
    """
    print("正在进行传统线性插值...")
    
    # 对所有价格列进行独立的线性插值
    for col in ['open', 'close', 'high', 'low']:
        merged_df[col] = merged_df[col].interpolate(method='linear')
    
    # 确保OHLC逻辑正确
    for i in range(len(merged_df)):
        open_price = merged_df['open'].iloc[i]
        close_price = merged_df['close'].iloc[i]
        high_price = merged_df['high'].iloc[i]
        low_price = merged_df['low'].iloc[i]

        # 确保high >= max(open, close) 且 low <= min(open, close)
        corrected_high = max(high_price, open_price, close_price)
        corrected_low = min(low_price, open_price, close_price)

        merged_df.iloc[i, merged_df.columns.get_loc('high')] = corrected_high
        merged_df.iloc[i, merged_df.columns.get_loc('low')] = corrected_low
    
    print("传统插值完成")
    return merged_df

def analyze_shadow_lengths(df, title=""):
    """
    分析影线长度统计
    """
    shadow_stats = {
        'upper_shadows': [],
        'lower_shadows': [],
        'shadow_ratios': [],
        'volatilities': []
    }
    
    for i in range(len(df)):
        open_price = df['open'].iloc[i]
        close_price = df['close'].iloc[i]
        high_price = df['high'].iloc[i]
        low_price = df['low'].iloc[i]
        
        # 计算影线长度
        body_high = max(open_price, close_price)
        body_low = min(open_price, close_price)
        body_size = abs(close_price - open_price)
        
        upper_shadow = high_price - body_high
        lower_shadow = body_low - low_price
        
        shadow_stats['upper_shadows'].append(upper_shadow)
        shadow_stats['lower_shadows'].append(lower_shadow)
        
        # 计算影线与实体的比例
        if body_size > 0:
            upper_ratio = upper_shadow / body_size
            lower_ratio = lower_shadow / body_size
            shadow_stats['shadow_ratios'].append(max(upper_ratio, lower_ratio))
        else:
            shadow_stats['shadow_ratios'].append(0)
        
        # 计算波动率 (high/low - 1)
        if low_price > 0:
            volatility = high_price / low_price - 1
            shadow_stats['volatilities'].append(volatility)
    
    # 统计分析
    avg_upper_shadow = np.mean(shadow_stats['upper_shadows'])
    avg_lower_shadow = np.mean(shadow_stats['lower_shadows'])
    max_shadow_ratio = max(shadow_stats['shadow_ratios'])
    avg_shadow_ratio = np.mean(shadow_stats['shadow_ratios'])
    avg_volatility = np.mean(shadow_stats['volatilities'])
    max_volatility = max(shadow_stats['volatilities'])
    
    # 统计超过阈值的K线数量
    long_shadow_count = sum(1 for ratio in shadow_stats['shadow_ratios'] if ratio > 1.0)
    high_vol_count = sum(1 for vol in shadow_stats['volatilities'] if vol > 0.01)
    
    print(f"\n=== {title} 影线分析 ===")
    print(f"平均上影线长度: {avg_upper_shadow:.6f}")
    print(f"平均下影线长度: {avg_lower_shadow:.6f}")
    print(f"平均影线/实体比例: {avg_shadow_ratio:.2f}")
    print(f"最大影线/实体比例: {max_shadow_ratio:.2f}")
    print(f"影线超过实体的K线数量: {long_shadow_count} ({long_shadow_count/len(df)*100:.1f}%)")
    print(f"平均波动率: {avg_volatility:.4f} ({avg_volatility*100:.2f}%)")
    print(f"最大波动率: {max_volatility:.4f} ({max_volatility*100:.2f}%)")
    print(f"波动率超过1%的K线: {high_vol_count} ({high_vol_count/len(df)*100:.1f}%)")
    
    return shadow_stats

def create_gaps_in_data(df, gap_percentage=0.1):
    """
    在数据中人为创建间隙来测试插值效果
    """
    df_with_gaps = df.copy()
    total_rows = len(df)
    gap_count = int(total_rows * gap_percentage)
    
    # 随机选择要删除的行
    np.random.seed(42)  # 固定随机种子以便重现
    gap_indices = np.random.choice(range(1, total_rows-1), gap_count, replace=False)
    
    # 将选中的行的OHLC数据设为NaN
    for idx in gap_indices:
        df_with_gaps.iloc[idx, df_with_gaps.columns.get_loc('high')] = np.nan
        df_with_gaps.iloc[idx, df_with_gaps.columns.get_loc('low')] = np.nan
        df_with_gaps.iloc[idx, df_with_gaps.columns.get_loc('open')] = np.nan
        df_with_gaps.iloc[idx, df_with_gaps.columns.get_loc('close')] = np.nan
    
    print(f"创建了 {gap_count} 个数据间隙用于测试插值效果")
    return df_with_gaps, gap_indices

def plot_comparison(df_original, df_traditional, df_smart, gap_indices, save_filename="interpolation_comparison.png"):
    """
    绘制插值方法对比图
    """
    fig, axes = plt.subplots(3, 1, figsize=(16, 12), sharex=True)
    
    # 转换时间戳
    df_original['datetime'] = pd.to_datetime(df_original['timestamp_sec'], unit='s')
    df_traditional['datetime'] = pd.to_datetime(df_traditional['timestamp_sec'], unit='s')
    df_smart['datetime'] = pd.to_datetime(df_smart['timestamp_sec'], unit='s')
    
    # 选择一个时间段进行展示（前200根K线）
    plot_range = slice(0, 200)
    
    datasets = [
        (df_original.iloc[plot_range], "原始数据", axes[0]),
        (df_traditional.iloc[plot_range], "传统线性插值", axes[1]),
        (df_smart.iloc[plot_range], "智能插值", axes[2])
    ]
    
    for df_plot, title, ax in datasets:
        # 绘制K线
        for i in range(len(df_plot)):
            row = df_plot.iloc[i]
            x = row['datetime']
            open_price = row['open']
            high_price = row['high']
            low_price = row['low']
            close_price = row['close']
            
            # 确定颜色
            color = 'red' if close_price >= open_price else 'green'
            
            # 绘制影线
            ax.plot([x, x], [low_price, high_price], color='black', linewidth=0.8)
            
            # 绘制实体
            body_height = abs(close_price - open_price)
            body_bottom = min(open_price, close_price)
            
            if body_height > 0:
                from matplotlib.patches import Rectangle
                rect = Rectangle((mdates.date2num(x) - 0.0003, body_bottom),
                               0.0006, body_height,
                               facecolor=color, edgecolor='black', linewidth=0.5, alpha=0.8)
                ax.add_patch(rect)
        
        # 标记插值点
        if title != "原始数据":
            gap_mask = [i for i in gap_indices if i < len(df_plot)]
            if gap_mask:
                gap_times = [df_plot.iloc[i]['datetime'] for i in gap_mask]
                gap_prices = [df_plot.iloc[i]['close'] for i in gap_mask]
                ax.scatter(gap_times, gap_prices, color='orange', s=50, alpha=0.8, 
                          label=f'插值点 ({len(gap_mask)}个)', zorder=5)
        
        ax.set_title(title, fontsize=14, fontweight='bold')
        ax.set_ylabel('价格 (USDT)', fontsize=12)
        ax.grid(True, alpha=0.3)
        ax.legend()
    
    # 设置x轴
    axes[-1].set_xlabel('时间', fontsize=12)
    axes[-1].xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
    
    plt.tight_layout()
    plt.savefig(save_filename, dpi=300, bbox_inches='tight')
    print(f"对比图已保存为: {save_filename}")
    plt.show()

def main():
    """
    主函数：测试改进的插值算法
    """
    print("=== K线插值算法对比测试 ===\n")
    
    # 读取原始数据
    print("1. 读取原始K线数据...")
    df_original = pd.read_csv('bybusdt_1min_kline_2025-07-03.csv')
    print(f"原始数据: {len(df_original)} 根K线")
    
    # 分析原始数据的影线情况
    original_stats = analyze_shadow_lengths(df_original, "原始数据")
    
    # 创建测试数据（人为添加间隙）
    print("\n2. 创建测试数据...")
    df_with_gaps, gap_indices = create_gaps_in_data(df_original, gap_percentage=0.05)  # 5%的数据缺失
    
    # 测试传统插值方法
    print("\n3. 测试传统线性插值...")
    df_traditional = df_with_gaps.copy()
    df_traditional = traditional_interpolation(df_traditional)
    traditional_stats = analyze_shadow_lengths(df_traditional, "传统插值")
    
    # 测试智能插值方法
    print("\n4. 测试智能插值...")
    df_smart = df_with_gaps.copy()
    df_smart = smart_kline_interpolation(df_smart)
    smart_stats = analyze_shadow_lengths(df_smart, "智能插值")
    
    # 对比分析
    print("\n=== 插值方法对比分析 ===")
    print(f"传统插值 vs 智能插值:")
    
    trad_long_shadows = sum(1 for ratio in traditional_stats['shadow_ratios'] if ratio > 1.0)
    smart_long_shadows = sum(1 for ratio in smart_stats['shadow_ratios'] if ratio > 1.0)
    
    trad_high_vol = sum(1 for vol in traditional_stats['volatilities'] if vol > 0.01)
    smart_high_vol = sum(1 for vol in smart_stats['volatilities'] if vol > 0.01)
    
    print(f"长影线K线数量: {trad_long_shadows} → {smart_long_shadows} (减少 {trad_long_shadows - smart_long_shadows})")
    print(f"高波动K线数量: {trad_high_vol} → {smart_high_vol} (减少 {trad_high_vol - smart_high_vol})")
    print(f"平均影线比例: {np.mean(traditional_stats['shadow_ratios']):.2f} → {np.mean(smart_stats['shadow_ratios']):.2f}")
    print(f"最大影线比例: {max(traditional_stats['shadow_ratios']):.2f} → {max(smart_stats['shadow_ratios']):.2f}")
    
    # 绘制对比图
    print("\n5. 生成对比图表...")
    plot_comparison(df_original, df_traditional, df_smart, gap_indices, 
                   "interpolation_comparison_test.png")
    
    print("\n=== 测试完成 ===")
    print("结论: 智能插值算法有效减少了插值后的长影线问题，提高了K线数据的合理性。")

if __name__ == "__main__":
    main()
