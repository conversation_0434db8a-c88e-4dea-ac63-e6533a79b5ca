import pandas as pd
import numpy as np
from datetime import datetime
import os

def generate_analysis_report():
    """生成K线分析报告"""
    
    print("=" * 80)
    print("BYBUSDT 1分钟K线数据分析报告")
    print("=" * 80)
    print(f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 1. 数据源分析
    print("1. 数据源分析")
    print("-" * 40)
    
    try:
        # 读取原始K线数据
        df_original = pd.read_csv('bybusdt_1min_kline_2025-07-03.csv')
        print(f"✓ 原始K线数据: {len(df_original)} 条记录")
        print(f"  文件: bybusdt_1min_kline_2025-07-03.csv")
        
        # 时间范围
        start_time = datetime.fromtimestamp(df_original['timestamp_sec'].min())
        end_time = datetime.fromtimestamp(df_original['timestamp_sec'].max())
        duration = end_time - start_time
        
        print(f"  时间范围: {start_time} 到 {end_time}")
        print(f"  持续时间: {duration}")
        print(f"  数据完整性: {len(df_original)} / {int(duration.total_seconds() / 60) + 1} 分钟")
        
    except FileNotFoundError:
        print("✗ 未找到原始K线数据文件")
        return
    
    print()
    
    # 2. 价格统计分析
    print("2. 价格统计分析")
    print("-" * 40)
    
    open_price = df_original['open'].iloc[0]
    close_price = df_original['close'].iloc[-1]
    high_price = df_original['high'].max()
    low_price = df_original['low'].min()
    price_range = high_price - low_price
    price_change = close_price - open_price
    price_change_pct = (price_change / open_price) * 100
    
    print(f"开盘价: {open_price:.6f} USDT")
    print(f"收盘价: {close_price:.6f} USDT")
    print(f"最高价: {high_price:.6f} USDT")
    print(f"最低价: {low_price:.6f} USDT")
    print(f"价格振幅: {price_range:.6f} USDT ({price_range/low_price*100:.2f}%)")
    print(f"涨跌幅: {price_change:+.6f} USDT ({price_change_pct:+.2f}%)")
    
    # 涨跌K线统计
    up_candles = (df_original['close'] > df_original['open']).sum()
    down_candles = (df_original['close'] < df_original['open']).sum()
    flat_candles = (df_original['close'] == df_original['open']).sum()
    
    print(f"上涨K线: {up_candles} 根 ({up_candles/len(df_original)*100:.1f}%)")
    print(f"下跌K线: {down_candles} 根 ({down_candles/len(df_original)*100:.1f}%)")
    print(f"平盘K线: {flat_candles} 根 ({flat_candles/len(df_original)*100:.1f}%)")
    
    print()
    
    # 3. 交易量分析
    print("3. 交易量分析")
    print("-" * 40)
    
    total_volume = df_original['vol'].sum()
    total_amount = df_original['amount'].sum()
    avg_volume = df_original['vol'].mean()
    max_volume = df_original['vol'].max()
    avg_price = total_amount / total_volume if total_volume > 0 else 0
    
    print(f"总交易量: {total_volume:,.2f} BYB")
    print(f"总交易额: {total_amount:,.2f} USDT")
    print(f"平均成交价: {avg_price:.6f} USDT")
    print(f"平均每分钟交易量: {avg_volume:,.2f} BYB")
    print(f"最大单分钟交易量: {max_volume:,.2f} BYB")
    
    # 找出交易量最大的时间段
    max_vol_idx = df_original['vol'].idxmax()
    max_vol_time = datetime.fromtimestamp(df_original.loc[max_vol_idx, 'timestamp_sec'])
    print(f"最大交易量时间: {max_vol_time}")
    
    print()
    
    # 4. 插针检测与优化分析
    print("4. 插针检测与优化分析")
    print("-" * 40)
    
    try:
        # 读取优化后的数据
        df_optimized = pd.read_csv('bybusdt_1min_kline_spike_optimized.csv')
        
        # 检测插针数量
        spike_count = 0
        spike_details = []
        
        for i in range(len(df_original)):
            original_high = df_original.iloc[i]['high']
            original_low = df_original.iloc[i]['low']
            optimized_high = df_optimized.iloc[i]['high']
            optimized_low = df_optimized.iloc[i]['low']
            
            if abs(original_high - optimized_high) > 1e-6 or abs(original_low - optimized_low) > 1e-6:
                spike_count += 1
                timestamp = datetime.fromtimestamp(df_original.iloc[i]['timestamp_sec'])
                
                if abs(original_high - optimized_high) > 1e-6:
                    spike_type = "上插针"
                    reduction = original_high - optimized_high
                    reduction_pct = reduction / original_high * 100
                else:
                    spike_type = "下插针"
                    reduction = optimized_low - original_low
                    reduction_pct = reduction / original_low * 100
                
                spike_details.append({
                    'time': timestamp,
                    'type': spike_type,
                    'reduction': reduction,
                    'reduction_pct': reduction_pct
                })
        
        print(f"检测到插针数量: {spike_count} 个")
        
        if spike_count > 0:
            # 优化效果统计
            original_range = df_original['high'].max() - df_original['low'].min()
            optimized_range = df_optimized['high'].max() - df_optimized['low'].min()
            range_reduction = original_range - optimized_range
            range_reduction_pct = range_reduction / original_range * 100
            
            print(f"原始价格振幅: {original_range:.6f} USDT")
            print(f"优化后价格振幅: {optimized_range:.6f} USDT")
            print(f"振幅减少: {range_reduction:.6f} USDT ({range_reduction_pct:.2f}%)")
            
            # 显示前5个最大的插针
            spike_details.sort(key=lambda x: x['reduction'], reverse=True)
            print(f"\n最大的5个插针:")
            for i, spike in enumerate(spike_details[:5], 1):
                print(f"  {i}. {spike['time']} - {spike['type']}")
                print(f"     优化幅度: {spike['reduction']:.6f} USDT ({spike['reduction_pct']:.2f}%)")
        
    except FileNotFoundError:
        print("✗ 未找到插针优化数据文件")
    
    print()
    
    # 5. 生成的文件清单
    print("5. 生成的文件清单")
    print("-" * 40)
    
    files_info = [
        ('bybusdt_1min_kline_2025-07-03.csv', '原始1分钟K线数据'),
        ('bybusdt_kline_chart_*.png', '静态K线图'),
        ('bybusdt_interactive_kline_*.html', '交互式K线图'),
        ('bybusdt_spike_comparison_*.png', '插针优化对比图'),
        ('bybusdt_1min_kline_spike_optimized.csv', '插针优化后的K线数据'),
        ('bybusdt_1min_kline_interactive_optimized.csv', '交互式图表优化数据')
    ]
    
    for pattern, description in files_info:
        if '*' in pattern:
            # 查找匹配的文件
            import glob
            matching_files = glob.glob(pattern)
            if matching_files:
                for file in matching_files:
                    if os.path.exists(file):
                        size = os.path.getsize(file)
                        print(f"✓ {file} - {description} ({size:,} bytes)")
            else:
                print(f"✗ {pattern} - {description} (未找到)")
        else:
            if os.path.exists(pattern):
                size = os.path.getsize(pattern)
                print(f"✓ {pattern} - {description} ({size:,} bytes)")
            else:
                print(f"✗ {pattern} - {description} (未找到)")
    
    print()
    
    # 6. 技术指标分析
    print("6. 技术指标分析")
    print("-" * 40)
    
    # 计算移动平均线
    df_original['ma5'] = df_original['close'].rolling(window=5).mean()
    df_original['ma20'] = df_original['close'].rolling(window=20).mean()
    
    # 当前价格相对于移动平均线的位置
    current_price = df_original['close'].iloc[-1]
    ma5_current = df_original['ma5'].iloc[-1]
    ma20_current = df_original['ma20'].iloc[-1]
    
    print(f"当前价格: {current_price:.6f} USDT")
    print(f"5日均线: {ma5_current:.6f} USDT ({(current_price/ma5_current-1)*100:+.2f}%)")
    print(f"20日均线: {ma20_current:.6f} USDT ({(current_price/ma20_current-1)*100:+.2f}%)")
    
    # 波动率分析
    returns = df_original['close'].pct_change().dropna()
    volatility = returns.std()
    print(f"价格波动率: {volatility:.6f} ({volatility*100:.4f}%)")
    
    print()
    
    # 7. 总结与建议
    print("7. 总结与建议")
    print("-" * 40)
    
    print("数据质量评估:")
    if len(df_original) > 300:
        print("✓ 数据量充足，适合进行技术分析")
    else:
        print("⚠ 数据量较少，分析结果可能有限")
    
    if spike_count > 0:
        print(f"✓ 成功检测并优化了 {spike_count} 个插针，提高了数据质量")
        print(f"✓ 价格振幅减少了 {range_reduction_pct:.2f}%，降低了异常波动")
    
    if price_change_pct > 5:
        print("⚠ 价格波动较大，建议关注风险控制")
    elif price_change_pct < -5:
        print("⚠ 价格下跌较多，建议谨慎操作")
    else:
        print("✓ 价格波动在合理范围内")
    
    print("\n建议:")
    print("• 使用优化后的K线数据进行技术分析，以获得更准确的结果")
    print("• 关注交易量异常时段，可能存在重要的市场信息")
    print("• 结合多个时间周期进行分析，提高判断准确性")
    print("• 定期更新数据，保持分析的时效性")
    
    print()
    print("=" * 80)
    print("报告生成完成")
    print("=" * 80)

if __name__ == "__main__":
    generate_analysis_report()
