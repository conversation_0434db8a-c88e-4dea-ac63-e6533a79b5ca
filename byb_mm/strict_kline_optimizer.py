import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def strict_ohlc_optimization(df, max_volatility=0.02):
    """
    严格的OHLC优化，确保每根K线波动不超过指定限制
    Args:
        df: K线数据DataFrame
        max_volatility: 最大允许波动率 (默认2%)
    Returns:
        优化后的DataFrame
    """
    df_optimized = df.copy()
    
    print(f"开始严格OHLC优化，确保所有K线波动≤{max_volatility*100:.1f}%...")
    
    # 第一步：平滑收盘价序列
    smoothing_factor = 0.7  # 较强的平滑
    smoothed_close = [df_optimized['close'].iloc[0]]
    
    for i in range(1, len(df_optimized)):
        prev_close = smoothed_close[-1]
        current_close = df_optimized['close'].iloc[i]
        
        # 限制收盘价变化幅度
        max_change = prev_close * max_volatility
        if abs(current_close - prev_close) > max_change:
            if current_close > prev_close:
                new_close = prev_close + max_change
            else:
                new_close = prev_close - max_change
        else:
            # 应用平滑
            new_close = smoothing_factor * prev_close + (1 - smoothing_factor) * current_close
        
        smoothed_close.append(new_close)
    
    df_optimized['close'] = smoothed_close
    
    # 第二步：基于平滑收盘价调整开盘价
    df_optimized.iloc[0, df_optimized.columns.get_loc('open')] = df_optimized['close'].iloc[0]
    
    for i in range(1, len(df_optimized)):
        prev_close = df_optimized['close'].iloc[i-1]
        current_open = df_optimized['open'].iloc[i]
        
        # 限制开盘价跳空
        max_gap = prev_close * max_volatility * 0.5  # 开盘跳空限制更严格
        if abs(current_open - prev_close) > max_gap:
            if current_open > prev_close:
                new_open = prev_close + max_gap
            else:
                new_open = prev_close - max_gap
            df_optimized.iloc[i, df_optimized.columns.get_loc('open')] = new_open
    
    # 第三步：严格控制每根K线的高低价
    optimization_count = 0
    
    for i in range(len(df_optimized)):
        open_price = df_optimized['open'].iloc[i]
        close_price = df_optimized['close'].iloc[i]
        
        # 计算基准价格（开盘和收盘的平均值）
        base_price = (open_price + close_price) / 2
        
        # 计算允许的最大波动范围
        max_range = base_price * max_volatility
        
        # 确保高低价包含开盘价和收盘价
        min_high = max(open_price, close_price)
        max_low = min(open_price, close_price)
        
        # 计算理想的高低价
        ideal_high = base_price + max_range / 2
        ideal_low = base_price - max_range / 2
        
        # 调整高价
        new_high = max(min_high, ideal_high)
        if new_high - base_price > max_range / 2:
            new_high = base_price + max_range / 2
            new_high = max(new_high, min_high)  # 确保包含开盘收盘价
        
        # 调整低价
        new_low = min(max_low, ideal_low)
        if base_price - new_low > max_range / 2:
            new_low = base_price - max_range / 2
            new_low = min(new_low, max_low)  # 确保包含开盘收盘价
        
        # 最终检查：如果高低价差仍然超过限制，强制调整
        if (new_high - new_low) / base_price > max_volatility:
            # 以开盘收盘价为基准，严格限制范围
            oc_mid = (open_price + close_price) / 2
            oc_range = abs(open_price - close_price)
            
            if oc_range / oc_mid <= max_volatility:
                # 开盘收盘价差在限制内，调整高低价
                remaining_range = oc_mid * max_volatility - oc_range
                extension = remaining_range / 2
                
                new_high = max(open_price, close_price) + extension
                new_low = min(open_price, close_price) - extension
            else:
                # 开盘收盘价差已超限，强制压缩
                target_range = oc_mid * max_volatility
                new_high = oc_mid + target_range / 2
                new_low = oc_mid - target_range / 2
                
                # 确保仍然包含开盘收盘价（可能需要微调开盘收盘价）
                if open_price > new_high:
                    new_high = open_price
                    new_low = new_high - target_range
                elif close_price > new_high:
                    new_high = close_price
                    new_low = new_high - target_range
                
                if open_price < new_low:
                    new_low = open_price
                    new_high = new_low + target_range
                elif close_price < new_low:
                    new_low = close_price
                    new_high = new_low + target_range
        
        # 应用调整
        df_optimized.iloc[i, df_optimized.columns.get_loc('high')] = new_high
        df_optimized.iloc[i, df_optimized.columns.get_loc('low')] = new_low
        
        # 验证调整效果
        final_volatility = (new_high - new_low) / base_price
        if final_volatility > max_volatility * 1.01:  # 允许1%的误差
            optimization_count += 1
    
    # 统计优化效果
    original_volatilities = []
    optimized_volatilities = []
    over_limit_count = 0
    
    for i in range(len(df)):
        # 原始波动率
        orig_base = (df['open'].iloc[i] + df['close'].iloc[i]) / 2
        if orig_base > 0:
            orig_vol = (df['high'].iloc[i] - df['low'].iloc[i]) / orig_base
            original_volatilities.append(orig_vol)
        
        # 优化后波动率
        opt_base = (df_optimized['open'].iloc[i] + df_optimized['close'].iloc[i]) / 2
        if opt_base > 0:
            opt_vol = (df_optimized['high'].iloc[i] - df_optimized['low'].iloc[i]) / opt_base
            optimized_volatilities.append(opt_vol)
            
            if opt_vol > max_volatility:
                over_limit_count += 1
    
    avg_original_vol = np.mean(original_volatilities) if original_volatilities else 0
    avg_optimized_vol = np.mean(optimized_volatilities) if optimized_volatilities else 0
    max_original_vol = max(original_volatilities) if original_volatilities else 0
    max_optimized_vol = max(optimized_volatilities) if optimized_volatilities else 0
    
    original_over_limit = sum(1 for vol in original_volatilities if vol > max_volatility)
    
    print(f"严格OHLC优化完成:")
    print(f"  平均波动率: {avg_original_vol:.4f} → {avg_optimized_vol:.4f}")
    print(f"  最大波动率: {max_original_vol:.4f} → {max_optimized_vol:.4f}")
    print(f"  超过{max_volatility*100:.1f}%限制的K线: {original_over_limit} → {over_limit_count}")
    
    if over_limit_count == 0:
        print(f"✓ 所有K线波动已严格控制在{max_volatility*100:.1f}%以内")
    else:
        print(f"⚠ 仍有{over_limit_count}根K线超过限制")
        
        # 显示超限的K线详情
        print("超限K线详情:")
        for i, vol in enumerate(optimized_volatilities):
            if vol > max_volatility:
                timestamp = datetime.fromtimestamp(df_optimized['timestamp_sec'].iloc[i])
                print(f"  {timestamp}: {vol:.4f} ({vol*100:.2f}%)")
    
    return df_optimized

def main():
    """主函数"""
    try:
        # 读取原始K线数据
        print("正在读取原始K线数据...")
        df = pd.read_csv('bybusdt_1min_kline_2025-07-03.csv')
        print(f"读取到 {len(df)} 条K线数据")
        
        # 应用严格优化 (设为1.99%确保严格小于2%)
        df_optimized = strict_ohlc_optimization(df, max_volatility=0.0199)
        
        # 保存优化后的数据
        output_file = 'bybusdt_1min_kline_strict_optimized.csv'
        df_optimized.to_csv(output_file, index=False)
        print(f"\n严格优化后的K线数据已保存为: {output_file}")
        
        # 显示前10条数据
        print("\n前10条优化后的K线数据:")
        print(df_optimized[['timestamp_sec', 'open', 'close', 'high', 'low']].head(10).to_string(index=False))
        
        # 最终验证
        print(f"\n=== 最终验证 ===")
        violation_count = 0
        max_actual_vol = 0
        
        for i in range(len(df_optimized)):
            base_price = (df_optimized['open'].iloc[i] + df_optimized['close'].iloc[i]) / 2
            if base_price > 0:
                volatility = (df_optimized['high'].iloc[i] - df_optimized['low'].iloc[i]) / base_price
                max_actual_vol = max(max_actual_vol, volatility)
                
                if volatility >= 0.02:  # 大于等于2%算超限
                    violation_count += 1

        print(f"最大实际波动率: {max_actual_vol:.4f} ({max_actual_vol*100:.2f}%)")
        print(f"超过2%限制的K线数: {violation_count}")

        if violation_count == 0:
            print("✅ 成功！所有K线波动都严格小于2%")
        else:
            print(f"❌ 仍有{violation_count}根K线达到或超过2%限制")
        
    except FileNotFoundError:
        print("错误: 未找到文件 'bybusdt_1min_kline_2025-07-03.csv'")
        print("请先运行 kline_restore.py 生成原始K线数据")
    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
