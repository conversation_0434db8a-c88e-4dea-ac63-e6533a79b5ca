K线数据完整性检查和填补报告
==================================================
生成时间: 2025-07-04 09:41:49

原始数据信息:
  总记录数: 412
  时间范围: 2025-07-04 17:00:00 到 2025-07-05 00:00:00
  期望记录数: 421

完整性检查结果:
  缺失记录数: 10
  重复记录数: 0
  不规则间隔数: 6

缺失数据详情:
  1. 2025-07-04 17:50:00 (时间戳: 1751622600.0)
  2. 2025-07-04 17:51:00 (时间戳: 1751622660.0)
  3. 2025-07-04 17:52:00 (时间戳: 1751622720.0)
  4. 2025-07-04 18:40:00 (时间戳: 1751625600.0)
  5. 2025-07-04 18:41:00 (时间戳: 1751625660.0)
  6. 2025-07-04 19:30:00 (时间戳: 1751628600.0)
  7. 2025-07-04 20:20:00 (时间戳: 1751631600.0)
  8. 2025-07-04 20:21:00 (时间戳: 1751631660.0)
  9. 2025-07-04 20:22:00 (时间戳: 1751631720.0)
  10. 2025-07-04 20:23:00 (时间戳: 1751631780.0)

数据填补结果:
  原始记录数: 412
  填补后记录数: 422
  新增记录数: 10
  时间连续性: 未通过
  价格连续性: 未通过
  交易量合理性: 通过

发现的问题:
  - 仍有 2 个不规则时间间隔
  - 发现 220 个异常价格跳跃（>10%）

建议:
  - 建议使用智能填补功能修复缺失数据
  - 检查数据源，确认不规则时间间隔的原因
