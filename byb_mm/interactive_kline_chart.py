import pandas as pd
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def detect_and_optimize_spikes(df, spike_threshold=2.5):
    """
    检测并优化插针数据
    Args:
        df: K线数据DataFrame
        spike_threshold: 插针检测阈值（标准差倍数）
    Returns:
        优化后的DataFrame和插针信息
    """
    df_optimized = df.copy()
    spike_info = []
    
    # 计算价格的移动平均和标准差
    window = min(20, len(df) // 4)
    df_optimized['price_ma'] = df_optimized['colse 收盘价'].rolling(window=window, center=True).mean()
    df_optimized['price_std'] = df_optimized['colse 收盘价'].rolling(window=window, center=True).std()
    
    # 填充边界值
    df_optimized['price_ma'] = df_optimized['price_ma'].fillna(method='bfill').fillna(method='ffill')
    df_optimized['price_std'] = df_optimized['price_std'].fillna(method='bfill').fillna(method='ffill')
    
    spike_count = 0
    
    for i in range(len(df_optimized)):
        row = df_optimized.iloc[i]
        ma = row['price_ma']
        std = row['price_std']
        timestamp = row['时间戳(秒)']
        
        # 检测高价插针
        if row['high 最高价'] > ma + spike_threshold * std:
            original_high = row['high 最高价']
            if i > 0 and i < len(df_optimized) - 1:
                prev_high = df_optimized.iloc[i-1]['high 最高价']
                next_high = df_optimized.iloc[i+1]['high 最高价']
                optimized_high = min(original_high, max(prev_high, next_high, ma + 1.5 * std))
            else:
                optimized_high = min(original_high, ma + 1.5 * std)
            
            df_optimized.iloc[i, df_optimized.columns.get_loc('high 最高价')] = optimized_high
            spike_info.append({
                'timestamp': timestamp,
                'type': '上插针',
                'original': original_high,
                'optimized': optimized_high,
                'reduction': original_high - optimized_high
            })
            spike_count += 1
        
        # 检测低价插针
        if row['low最低价'] < ma - spike_threshold * std:
            original_low = row['low最低价']
            if i > 0 and i < len(df_optimized) - 1:
                prev_low = df_optimized.iloc[i-1]['low最低价']
                next_low = df_optimized.iloc[i+1]['low最低价']
                optimized_low = max(original_low, min(prev_low, next_low, ma - 1.5 * std))
            else:
                optimized_low = max(original_low, ma - 1.5 * std)
            
            df_optimized.iloc[i, df_optimized.columns.get_loc('low最低价')] = optimized_low
            spike_info.append({
                'timestamp': timestamp,
                'type': '下插针',
                'original': original_low,
                'optimized': optimized_low,
                'reduction': original_low - optimized_low
            })
            spike_count += 1
    
    print(f"总共检测并优化了 {spike_count} 个插针")
    
    # 清理临时列
    df_optimized = df_optimized.drop(['price_ma', 'price_std'], axis=1)
    
    return df_optimized, spike_info

def create_interactive_kline_chart(df, title="BYBUSDT 1分钟K线图", optimize_spikes=True):
    """
    创建交互式K线图
    """
    # 数据预处理
    df_plot = df.copy()
    df_plot['datetime'] = pd.to_datetime(df_plot['时间戳(秒)'], unit='s')
    
    # 插针优化
    spike_info = []
    if optimize_spikes:
        print("正在检测和优化插针...")
        df_plot, spike_info = detect_and_optimize_spikes(df_plot)
        title += " (已优化插针)"
    
    # 创建子图
    fig = make_subplots(
        rows=3, cols=1,
        shared_xaxes=True,
        vertical_spacing=0.05,
        subplot_titles=('K线图', '交易量', '价格分布'),
        row_heights=[0.6, 0.25, 0.15]
    )
    
    # 1. K线图
    fig.add_trace(
        go.Candlestick(
            x=df_plot['datetime'],
            open=df_plot['open 开盘价'],
            high=df_plot['high 最高价'],
            low=df_plot['low最低价'],
            close=df_plot['colse 收盘价'],
            name='BYBUSDT',
            increasing_line_color='red',
            decreasing_line_color='green',
            increasing_fillcolor='red',
            decreasing_fillcolor='green'
        ),
        row=1, col=1
    )
    
    # 添加移动平均线
    ma_20 = df_plot['colse 收盘价'].rolling(window=20).mean()
    fig.add_trace(
        go.Scatter(
            x=df_plot['datetime'],
            y=ma_20,
            mode='lines',
            name='MA20',
            line=dict(color='blue', width=1),
            opacity=0.7
        ),
        row=1, col=1
    )
    
    # 标记插针位置
    if spike_info:
        spike_times = [datetime.fromtimestamp(s['timestamp']) for s in spike_info]
        spike_prices = []
        spike_colors = []
        spike_texts = []
        
        for s in spike_info:
            dt = datetime.fromtimestamp(s['timestamp'])
            idx = df_plot[df_plot['datetime'] == dt].index
            if len(idx) > 0:
                if s['type'] == '上插针':
                    spike_prices.append(df_plot.loc[idx[0], 'high 最高价'])
                    spike_colors.append('red')
                else:
                    spike_prices.append(df_plot.loc[idx[0], 'low最低价'])
                    spike_colors.append('blue')
                spike_texts.append(f"{s['type']}<br>原价: {s['original']:.6f}<br>优化后: {s['optimized']:.6f}")
        
        if spike_times:
            fig.add_trace(
                go.Scatter(
                    x=spike_times,
                    y=spike_prices,
                    mode='markers',
                    name='插针位置',
                    marker=dict(
                        symbol='triangle-up',
                        size=10,
                        color=spike_colors,
                        line=dict(width=2, color='white')
                    ),
                    text=spike_texts,
                    hovertemplate='%{text}<extra></extra>'
                ),
                row=1, col=1
            )
    
    # 2. 交易量柱状图
    colors = ['red' if df_plot.iloc[i]['colse 收盘价'] >= df_plot.iloc[i]['open 开盘价'] 
              else 'green' for i in range(len(df_plot))]
    
    fig.add_trace(
        go.Bar(
            x=df_plot['datetime'],
            y=df_plot['vol交易量'],
            name='交易量',
            marker_color=colors,
            opacity=0.7
        ),
        row=2, col=1
    )
    
    # 3. 价格分布直方图
    fig.add_trace(
        go.Histogram(
            x=df_plot['colse 收盘价'],
            nbinsx=50,
            name='价格分布',
            marker_color='lightblue',
            opacity=0.7
        ),
        row=3, col=1
    )
    
    # 更新布局
    fig.update_layout(
        title={
            'text': title,
            'x': 0.5,
            'xanchor': 'center',
            'font': {'size': 20}
        },
        height=1000,
        showlegend=True,
        xaxis_rangeslider_visible=False,
        template='plotly_white'
    )
    
    # 更新x轴
    fig.update_xaxes(
        title_text="时间",
        row=2, col=1
    )
    
    # 更新y轴
    fig.update_yaxes(title_text="价格 (USDT)", row=1, col=1)
    fig.update_yaxes(title_text="交易量 (BYB)", row=2, col=1)
    fig.update_yaxes(title_text="频次", row=3, col=1)
    
    # 添加统计信息注释
    stats_text = f"""
    数据统计:
    • 开盘: {df_plot['open 开盘价'].iloc[0]:.6f} USDT
    • 收盘: {df_plot['colse 收盘价'].iloc[-1]:.6f} USDT  
    • 最高: {df_plot['high 最高价'].max():.6f} USDT
    • 最低: {df_plot['low最低价'].min():.6f} USDT
    • 振幅: {((df_plot['high 最高价'].max() - df_plot['low最低价'].min()) / df_plot['low最低价'].min() * 100):.2f}%
    • 总交易量: {df_plot['vol交易量'].sum():.0f} BYB
    • 总交易额: {df_plot['amount交易额'].sum():.0f} USDT
    """
    
    fig.add_annotation(
        text=stats_text,
        xref="paper", yref="paper",
        x=0.02, y=0.98,
        showarrow=False,
        font=dict(size=10),
        bgcolor="rgba(255,255,255,0.8)",
        bordercolor="gray",
        borderwidth=1
    )
    
    return fig, df_plot, spike_info

def main():
    """主函数"""
    try:
        # 读取K线数据
        print("正在读取K线数据...")
        try:
            df = pd.read_csv('bybusdt_1min_kline_final.csv', encoding='utf-8')
        except UnicodeDecodeError:
            try:
                df = pd.read_csv('bybusdt_1min_kline_final.csv', encoding='gbk')
            except UnicodeDecodeError:
                df = pd.read_csv('bybusdt_1min_kline_final.csv', encoding='latin-1')
        
        print(f"成功读取 {len(df)} 条K线数据")
        
        # 创建交互式K线图
        print("正在创建交互式K线图...")
        fig, df_optimized, spike_info = create_interactive_kline_chart(
            df, 
            title="BYBUSDT 1分钟K线图 (交互式)", 
            optimize_spikes=True
        )
        
        # 保存HTML文件
        html_filename = f'bybusdt_interactive_kline_{datetime.now().strftime("%Y%m%d_%H%M%S")}.html'
        fig.write_html(html_filename)
        print(f"交互式K线图已保存为: {html_filename}")
        
        # 显示图表
        fig.show()
        
        # 输出插针优化报告
        if spike_info:
            print(f"\n=== 插针优化报告 ===")
            print(f"共检测到 {len(spike_info)} 个插针:")
            for i, spike in enumerate(spike_info, 1):
                print(f"{i}. {spike['type']} - 时间: {datetime.fromtimestamp(spike['timestamp'])}")
                print(f"   原价格: {spike['original']:.6f} -> 优化后: {spike['optimized']:.6f}")
                print(f"   优化幅度: {abs(spike['reduction']):.6f} ({abs(spike['reduction'])/spike['original']*100:.2f}%)")
        
        # 保存优化后的数据
        output_file = 'bybusdt_1min_kline_interactive_optimized.csv'
        df_optimized.drop('datetime', axis=1).to_csv(output_file, index=False)
        print(f"优化后的K线数据已保存为: {output_file}")
        
    except FileNotFoundError:
        print("错误: 未找到文件 'bybusdt_1min_kline_final.csv'")
        print("请确保文件在当前目录下")
    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
