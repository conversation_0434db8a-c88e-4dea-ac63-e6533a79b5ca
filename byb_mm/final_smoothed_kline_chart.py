import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def plot_final_smoothed_kline():
    """绘制最终平滑优化后的K线图"""
    
    # 读取平滑后的数据
    df = pd.read_csv('bybusdt_1min_kline_smoothed.csv')
    
    # 转换时间
    df['datetime'] = pd.to_datetime(df['datetime'])
    
    print(f"绘制平滑优化后的K线图，共 {len(df)} 根K线")
    
    # 创建图表
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(18, 14), 
                                        gridspec_kw={'height_ratios': [3, 1, 1]})
    
    # 1. 主图：K线图
    for i in range(len(df)):
        row = df.iloc[i]
        x = row['datetime']
        open_price = row['open']
        high_price = row['high']
        low_price = row['low']
        close_price = row['close']
        
        # 确定颜色：红涨绿跌
        color = 'red' if close_price >= open_price else 'green'
        
        # 绘制影线
        ax1.plot([x, x], [low_price, high_price], color='black', linewidth=0.8)
        
        # 绘制实体
        body_height = abs(close_price - open_price)
        body_bottom = min(open_price, close_price)
        
        if body_height > 0:
            rect = Rectangle((mdates.date2num(x) - 0.0003, body_bottom), 
                           0.0006, body_height, 
                           facecolor=color, edgecolor='black', linewidth=0.5, alpha=0.8)
            ax1.add_patch(rect)
        else:
            # 十字星
            ax1.plot([mdates.date2num(x) - 0.0003, mdates.date2num(x) + 0.0003], 
                    [open_price, open_price], color='black', linewidth=1)
    
    # 添加移动平均线
    ma5 = df['close'].rolling(window=5).mean()
    ma20 = df['close'].rolling(window=20).mean()
    
    ax1.plot(df['datetime'], ma5, color='blue', linewidth=1.5, alpha=0.8, label='MA5')
    ax1.plot(df['datetime'], ma20, color='orange', linewidth=1.5, alpha=0.8, label='MA20')
    
    # 设置主图
    ax1.set_title('BYBUSDT 1分钟K线图 (平滑优化版)', fontsize=16, fontweight='bold', pad=20)
    ax1.set_ylabel('价格 (USDT)', fontsize=12)
    ax1.legend(loc='upper left')
    ax1.grid(True, alpha=0.3)
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
    ax1.xaxis.set_major_locator(mdates.HourLocator(interval=2))
    
    # 添加价格统计信息
    price_info = f"开盘: {df['open'].iloc[0]:.6f} | " \
                f"收盘: {df['close'].iloc[-1]:.6f} | " \
                f"最高: {df['high'].max():.6f} | " \
                f"最低: {df['low'].min():.6f} | " \
                f"振幅: {((df['high'].max() - df['low'].min()) / df['low'].min() * 100):.2f}%"
    
    ax1.text(0.02, 0.98, price_info, transform=ax1.transAxes, 
            bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue", alpha=0.8),
            verticalalignment='top', fontsize=11, fontweight='bold')
    
    # 2. 副图：交易量
    colors = ['red' if df.iloc[i]['close'] >= df.iloc[i]['open'] 
              else 'green' for i in range(len(df))]
    
    ax2.bar(df['datetime'], df['volume'], 
           color=colors, alpha=0.7, width=pd.Timedelta(minutes=0.8))
    
    ax2.set_ylabel('交易量 (BYB)', fontsize=12)
    ax2.grid(True, alpha=0.3)
    ax2.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
    ax2.xaxis.set_major_locator(mdates.HourLocator(interval=2))
    
    # 3. 副图：价格波动率
    returns = df['close'].pct_change().abs() * 100  # 转换为百分比
    
    ax3.plot(df['datetime'][1:], returns[1:], color='purple', linewidth=1, alpha=0.8)
    ax3.fill_between(df['datetime'][1:], returns[1:], alpha=0.3, color='purple')
    ax3.axhline(y=2, color='red', linestyle='--', alpha=0.7, label='2%阈值')
    
    ax3.set_ylabel('价格波动率 (%)', fontsize=12)
    ax3.set_xlabel('时间', fontsize=12)
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    ax3.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
    ax3.xaxis.set_major_locator(mdates.HourLocator(interval=2))
    
    # 旋转x轴标签
    plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)
    plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45)
    plt.setp(ax3.xaxis.get_majorticklabels(), rotation=45)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    filename = f'bybusdt_final_smoothed_kline_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"最终平滑K线图已保存为: {filename}")
    
    # 显示图表
    plt.show()
    
    return filename

def analyze_smoothed_data():
    """分析平滑后的数据质量"""
    
    # 读取原始数据和平滑数据
    df_original = pd.read_csv('bybusdt_1min_kline.csv')
    df_smoothed = pd.read_csv('bybusdt_1min_kline_smoothed.csv')
    
    print("\n=== 平滑优化效果分析 ===")
    
    # 价格波动性分析
    original_volatility = df_original['close'].pct_change().std()
    smoothed_volatility = df_smoothed['close'].pct_change().std()
    
    # 最大单次变化
    original_max_change = df_original['close'].pct_change().abs().max()
    smoothed_max_change = df_smoothed['close'].pct_change().abs().max()
    
    # 价格范围
    original_range = df_original['high'].max() - df_original['low'].min()
    smoothed_range = df_smoothed['high'].max() - df_smoothed['low'].min()
    
    # 超过阈值的变动次数
    threshold = 0.02  # 2%
    original_large_moves = (df_original['close'].pct_change().abs() > threshold).sum()
    smoothed_large_moves = (df_smoothed['close'].pct_change().abs() > threshold).sum()
    
    print(f"波动率对比:")
    print(f"  原始数据: {original_volatility:.6f}")
    print(f"  平滑数据: {smoothed_volatility:.6f}")
    print(f"  改善程度: {(original_volatility - smoothed_volatility) / original_volatility * 100:.2f}%")
    
    print(f"\n最大单次变化:")
    print(f"  原始数据: {original_max_change:.4f} ({original_max_change*100:.2f}%)")
    print(f"  平滑数据: {smoothed_max_change:.4f} ({smoothed_max_change*100:.2f}%)")
    print(f"  改善程度: {(original_max_change - smoothed_max_change) / original_max_change * 100:.2f}%")
    
    print(f"\n价格范围:")
    print(f"  原始数据: {original_range:.6f} USDT")
    print(f"  平滑数据: {smoothed_range:.6f} USDT")
    
    print(f"\n超过{threshold*100}%的大幅变动:")
    print(f"  原始数据: {original_large_moves} 次")
    print(f"  平滑数据: {smoothed_large_moves} 次")
    print(f"  减少: {original_large_moves - smoothed_large_moves} 次")
    
    # 数据质量评级
    print(f"\n=== 数据质量评级 ===")
    
    quality_score = 0
    
    # 波动率评分 (30分)
    if smoothed_volatility < 0.005:
        volatility_score = 30
    elif smoothed_volatility < 0.01:
        volatility_score = 25
    elif smoothed_volatility < 0.02:
        volatility_score = 20
    else:
        volatility_score = 10
    quality_score += volatility_score
    
    # 最大变化评分 (30分)
    if smoothed_max_change < 0.03:
        max_change_score = 30
    elif smoothed_max_change < 0.05:
        max_change_score = 25
    elif smoothed_max_change < 0.08:
        max_change_score = 20
    else:
        max_change_score = 10
    quality_score += max_change_score
    
    # 大幅变动次数评分 (40分)
    large_move_ratio = smoothed_large_moves / len(df_smoothed)
    if large_move_ratio < 0.01:
        large_move_score = 40
    elif large_move_ratio < 0.03:
        large_move_score = 35
    elif large_move_ratio < 0.05:
        large_move_score = 30
    else:
        large_move_score = 20
    quality_score += large_move_score
    
    print(f"波动率评分: {volatility_score}/30")
    print(f"最大变化评分: {max_change_score}/30")
    print(f"大幅变动评分: {large_move_score}/40")
    print(f"总体质量评分: {quality_score}/100")
    
    if quality_score >= 90:
        grade = "A+ (优秀)"
    elif quality_score >= 80:
        grade = "A (良好)"
    elif quality_score >= 70:
        grade = "B (中等)"
    elif quality_score >= 60:
        grade = "C (及格)"
    else:
        grade = "D (需要改进)"
    
    print(f"数据质量等级: {grade}")
    
    # 技术分析适用性
    print(f"\n=== 技术分析适用性 ===")
    if quality_score >= 80:
        print("✓ 数据质量优秀，非常适合进行技术分析")
        print("✓ 可以安全使用各种技术指标")
        print("✓ 适合高频交易策略")
    elif quality_score >= 70:
        print("✓ 数据质量良好，适合大部分技术分析")
        print("⚠ 建议结合多个时间周期分析")
    else:
        print("⚠ 数据质量一般，建议谨慎使用")
        print("⚠ 可能需要进一步优化")

def main():
    """主函数"""
    try:
        print("正在生成最终平滑K线图...")
        
        # 绘制最终K线图
        chart_file = plot_final_smoothed_kline()
        
        # 分析数据质量
        analyze_smoothed_data()
        
        print(f"\n=== 文件生成完成 ===")
        print(f"平滑K线图: {chart_file}")
        print(f"平滑数据文件: bybusdt_1min_kline_smoothed.csv")
        
    except FileNotFoundError as e:
        print(f"错误: 未找到必要的文件 - {e}")
        print("请确保已运行价格平滑优化脚本")
    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
