import pandas as pd
import pymysql
import numpy as np
import random
import time
import asyncio
import logging
import traceback
import json
import os
from datetime import datetime, timedelta
from typing import List, Dict, Tuple
import pytz
import sys
sys.path.append(r'/home/<USER>/byb_mm/')
import backoff
import aiohttp

# 添加项目路径
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.append(project_root)
sys.path.append(current_dir)

from byex.spot.trade import SpotTrade
from byex.spot.market import Spot
import con_pri

# 时区设置
beijing_tz = pytz.timezone("Asia/Shanghai")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('byb_order_algorithm.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# 数据库连接信息
DB_CONFIG = {
    "host": "127.0.0.1",
    "port": 3306,
    "user": "weili",
    "password": "12345678",
    "database": "buy_back"
}

# 交易配置
TRADING_CONFIG = {
    "symbol": "manausdt",  # 交易对
    "side": "BUY",        # 买单
    "type": 2,            # 市价单
    "min_order_size": 100,  # 最小单次下单量（BYB）
    "max_order_size": 800,  # 最大单次下单量（BYB）
    "min_interval": 60,     # 最小下单间隔（秒）
    "max_interval": 3600,   # 最大下单间隔（秒）
}


@backoff.on_exception(backoff.expo, Exception, max_tries=3)
async def send_lark(msg, level='info'):
    """发送消息到Lark（飞书）"""
    headers = {'Content-Type': 'application/json'}
    payload_message = {
        "msg_type": "text",
        "content": {
            "text": f"【{level.upper()}】BYB下单算法\n{str(msg)}"
        }
    }
    webhook = 'https://open.larksuite.com/open-apis/bot/v2/hook/230d3321-92cb-44b4-8dcc-a77b2e2c4c2a'
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(webhook, data=json.dumps(payload_message), headers=headers, timeout=10) as response:
                res = await response.json()
                statuscode = res.get('StatusCode', 404)
                if statuscode != 0 or res["StatusMessage"] != 'success':
                    logging.error(f"Lark webhook response error: {res}")
    except Exception as e:
        logging.error(f"Lark notification error for message '{msg}': {str(e)}")


def schedule_lark_message(msg, level='info'):
    """在当前事件循环中调度Lark消息发送任务，不阻塞主程序"""
    try:
        loop = asyncio.get_event_loop()
        if loop.is_running():
            task = loop.create_task(send_lark(msg, level))
            task.add_done_callback(lambda t: None if not t.exception() else
                                 logging.error(f"Lark消息发送任务失败: {t.exception()}"))
        else:
            asyncio.run(send_lark(msg, level))
    except Exception as e:
        logging.error(f"调度Lark消息失败: {str(e)}")


class BYBOrderAlgorithm:
    """BYB智能下单算法类"""
    
    def __init__(self):
        """初始化下单算法"""
        self.spot_client = SpotTrade(con_pri.api_key, con_pri.api_secret)
        self.spot_market = Spot()
        SpotTrade.BASE_URL = "https://openapi.100exdemo.com"
        Spot.BASE_URL = "https://openapi.100exdemo.com"
        self.db_config = DB_CONFIG
        self.trading_config = TRADING_CONFIG
        
        # 算法状态
        self.is_running = False
        self.total_executed = 0
        self.orders_executed = 0
        self.start_time = None
        self.end_time = None
        
        # 订单计划
        self.order_schedule = []
        self.execution_log = []
        
        logging.info("BYB下单算法初始化完成")
    
    def get_buy_back_params(self) -> Dict:
        """从数据库获取回购参数"""
        try:
            conn = pymysql.connect(**self.db_config)
            with conn.cursor() as cursor:
                cursor.execute("SELECT * FROM buy_back_params ORDER BY created_at DESC LIMIT 1;")
                result = cursor.fetchone()
                if result:
                    params = {
                        'id': result[0],
                        'days': float(result[1]),
                        'total_amount': float(result[2]),
                        'each_amount': float(result[3]),
                        'created_at': result[4]
                    }
                    logging.info(f"获取数据库参数: {params}")
                    return params
                else:
                    raise ValueError("数据库中没有找到回购参数")
        except Exception as e:
            logging.error(f"获取数据库参数失败: {str(e)}")
            raise
        finally:
            if 'conn' in locals():
                conn.close()
    
    def generate_order_schedule(self, days: float, total_amount: float, each_amount: float = None) -> List[Dict]:
        """生成订单时间表

        Args:
            days: 总时间范围（天）
            total_amount: 总下单量（BYB）
            each_amount: 单次最大下单量（BYB），如果为None则使用默认配置

        Returns:
            订单时间表列表
        """
        try:
            # 计算时间范围
            start_time = datetime.now(beijing_tz)
            end_time = start_time + timedelta(days=days)
            total_seconds = int(days * 24 * 3600)

            # 确定单次下单量范围
            if each_amount is not None and each_amount > 0:
                max_order_size = each_amount
                min_order_size = min(self.trading_config["min_order_size"], each_amount * 0.5)  # 最小为each_amount的50%
            else:
                max_order_size = self.trading_config["max_order_size"]
                min_order_size = self.trading_config["min_order_size"]

            logging.info(f"生成订单计划: 开始时间={start_time}, 结束时间={end_time}, 总金额={total_amount}")
            logging.info(f"单次下单量范围: {min_order_size:.2f} - {max_order_size:.2f} BYB")

            # 分解订单
            remaining_amount = total_amount
            schedule = []
            current_time = start_time

            while remaining_amount > 0 and current_time < end_time:
                # 随机订单大小
                order_size = random.uniform(
                    min_order_size,
                    min(max_order_size, remaining_amount)
                )
                order_size = round(order_size, 2)
                
                # 随机时间间隔
                interval = random.uniform(
                    self.trading_config["min_interval"],
                    self.trading_config["max_interval"]
                )
                
                # 确保不超过结束时间
                next_time = current_time + timedelta(seconds=interval)
                if next_time > end_time:
                    next_time = end_time
                
                order = {
                    'scheduled_time': next_time,
                    'amount': order_size,
                    'symbol': self.trading_config["symbol"],
                    'side': self.trading_config["side"],
                    'type': self.trading_config["type"],
                    'status': 'pending'
                }
                
                schedule.append(order)
                remaining_amount -= order_size
                current_time = next_time
                
                # 防止无限循环
                if len(schedule) > 10000:
                    logging.warning("订单数量过多，停止生成")
                    break
            
            # 如果还有剩余金额，处理剩余部分
            if remaining_amount > 0:
                if schedule:
                    # 检查是否可以添加到最后一个订单而不超过each_amount限制
                    last_order = schedule[-1]
                    if each_amount is not None and (last_order['amount'] + remaining_amount) > each_amount:
                        # 如果会超过限制，创建新的订单来处理剩余金额
                        while remaining_amount > 0:
                            new_order_amount = min(remaining_amount, max_order_size)
                            new_order = {
                                'scheduled_time': end_time,  # 在结束时间执行
                                'amount': round(new_order_amount, 2),
                                'symbol': self.trading_config["symbol"],
                                'side': self.trading_config["side"],
                                'type': self.trading_config["type"],
                                'status': 'pending'
                            }
                            schedule.append(new_order)
                            remaining_amount -= new_order_amount
                            logging.info(f"创建新订单处理剩余金额: {new_order_amount:.2f} BYB")
                    else:
                        # 可以安全添加到最后一个订单
                        schedule[-1]['amount'] += remaining_amount
                        logging.info(f"剩余金额 {remaining_amount:.2f} 添加到最后一个订单")
                else:
                    # 没有现有订单，创建新订单
                    while remaining_amount > 0:
                        new_order_amount = min(remaining_amount, max_order_size)
                        new_order = {
                            'scheduled_time': end_time,
                            'amount': round(new_order_amount, 2),
                            'symbol': self.trading_config["symbol"],
                            'side': self.trading_config["side"],
                            'type': self.trading_config["type"],
                            'status': 'pending'
                        }
                        schedule.append(new_order)
                        remaining_amount -= new_order_amount
                        logging.info(f"创建订单处理剩余金额: {new_order_amount:.2f} BYB")
            
            logging.info(f"生成订单计划完成: 共 {len(schedule)} 个订单")
            return schedule
            
        except Exception as e:
            logging.error(f"生成订单计划失败: {str(e)}")
            raise
    
    async def execute_order(self, order: Dict) -> bool:
        """执行单个订单
        
        Args:
            order: 订单信息
            
        Returns:
            是否执行成功
        """
        try:
            logging.info(f"执行订单: {order['amount']} {order['symbol']} {order['side']}")
            
            # 执行下单
            result = await self.spot_client.async_new_order(
                symbol=order['symbol'],
                side=order['side'],
                type=order['type'],
                volume=str(order['amount'])
            )
            
            if result:
                order['status'] = 'completed'
                order['result'] = result
                order['executed_time'] = datetime.now(beijing_tz)
                self.total_executed += order['amount']
                self.orders_executed += 1
                
                logging.info(f"订单执行成功: 订单ID={result.get('orderId', 'N/A')}, 金额={order['amount']}")
                return True
            else:
                order['status'] = 'failed'
                order['error'] = 'API返回空结果'
                logging.error(f"订单执行失败: API返回空结果")
                return False
                
        except Exception as e:
            order['status'] = 'failed'
            order['error'] = str(e)
            logging.error(f"订单执行异常: {str(e)}")
            return False
    
    def save_execution_log(self):
        """保存执行日志到文件"""
        try:
            log_file = f"byb_order_execution_{datetime.now(beijing_tz).strftime('%Y%m%d_%H%M%S')}.json"
            log_data = {
                'start_time': self.start_time.isoformat() if self.start_time else None,
                'end_time': datetime.now(beijing_tz).isoformat(),
                'total_executed': self.total_executed,
                'orders_executed': self.orders_executed,
                'total_orders': len(self.order_schedule),
                'execution_log': []
            }
            
            for order in self.order_schedule:
                log_entry = order.copy()
                # 转换时间为字符串
                if 'scheduled_time' in log_entry:
                    log_entry['scheduled_time'] = log_entry['scheduled_time'].isoformat()
                if 'executed_time' in log_entry:
                    log_entry['executed_time'] = log_entry['executed_time'].isoformat()
                log_data['execution_log'].append(log_entry)
            
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(log_data, f, ensure_ascii=False, indent=2)
            
            logging.info(f"执行日志已保存到: {log_file}")
            
        except Exception as e:
            logging.error(f"保存执行日志失败: {str(e)}")

    async def run_algorithm(self):
        """运行下单算法主程序"""
        try:
            # 获取数据库参数
            params = self.get_buy_back_params()
            days = params['days']
            total_amount = params['total_amount']
            each_amount = params.get('each_amount', None)

            # 计算预计订单数
            if each_amount and each_amount > 0:
                avg_order_size = each_amount * 0.75  # 假设平均为each_amount的75%
            else:
                avg_order_size = (self.trading_config['min_order_size'] + self.trading_config['max_order_size']) / 2

            # 发送启动通知
            start_msg = (
                f"🚀 BYB下单算法启动\n"
                f"总时间范围: {days} 天\n"
                f"总下单量: {total_amount:,.2f} BYB\n"
                f"单次最大量: {each_amount:,.2f} BYB\n" if each_amount else f"单次量范围: {self.trading_config['min_order_size']}-{self.trading_config['max_order_size']} BYB\n"
                f"预计订单数: {int(total_amount / avg_order_size)}\n"
                f"启动时间: {datetime.now(beijing_tz).strftime('%Y-%m-%d %H:%M:%S')}"
            )
            schedule_lark_message(start_msg, level='info')

            # 生成订单计划
            self.order_schedule = self.generate_order_schedule(days, total_amount, each_amount)
            self.start_time = datetime.now(beijing_tz)
            self.is_running = True

            logging.info(f"开始执行订单计划，共 {len(self.order_schedule)} 个订单")

            # 执行订单
            for i, order in enumerate(self.order_schedule):
                if not self.is_running:
                    logging.info("算法被停止")
                    break

                # 等待到预定时间
                now = datetime.now(beijing_tz)
                if order['scheduled_time'] > now:
                    wait_seconds = (order['scheduled_time'] - now).total_seconds()
                    logging.info(f"等待 {wait_seconds:.1f} 秒执行第 {i+1} 个订单")
                    await asyncio.sleep(wait_seconds)

                # 执行订单
                success = await self.execute_order(order)

                # 记录进度
                progress = (i + 1) / len(self.order_schedule) * 100
                logging.info(f"订单执行进度: {progress:.1f}% ({i+1}/{len(self.order_schedule)})")

                # 每10个订单发送一次进度通知
                if (i + 1) % 10 == 0 or not success:
                    progress_msg = (
                        f"📊 下单进度更新\n"
                        f"进度: {progress:.1f}% ({i+1}/{len(self.order_schedule)})\n"
                        f"已执行金额: {self.total_executed:,.2f} BYB\n"
                        f"成功订单: {self.orders_executed}\n"
                        f"当前时间: {datetime.now(beijing_tz).strftime('%Y-%m-%d %H:%M:%S')}"
                    )
                    if not success:
                        progress_msg += f"\n⚠️ 订单执行失败: {order.get('error', '未知错误')}"

                    schedule_lark_message(progress_msg, level='info' if success else 'warning')

                # 短暂休息避免API限制
                await asyncio.sleep(1)

            # 完成通知
            completion_msg = (
                f"✅ BYB下单算法完成\n"
                f"总订单数: {len(self.order_schedule)}\n"
                f"成功订单: {self.orders_executed}\n"
                f"执行金额: {self.total_executed:,.2f} BYB\n"
                f"成功率: {(self.orders_executed/len(self.order_schedule)*100):.1f}%\n"
                f"完成时间: {datetime.now(beijing_tz).strftime('%Y-%m-%d %H:%M:%S')}"
            )
            schedule_lark_message(completion_msg, level='info')

            # 保存执行日志
            self.save_execution_log()

        except Exception as e:
            error_msg = f"下单算法执行异常: {str(e)}"
            logging.error(f"{error_msg}\n{traceback.format_exc()}")
            schedule_lark_message(error_msg, level='error')
        finally:
            self.is_running = False

    def stop_algorithm(self):
        """停止算法执行"""
        self.is_running = False
        logging.info("下单算法停止信号已发送")

    def get_status(self) -> Dict:
        """获取算法执行状态"""
        return {
            'is_running': self.is_running,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'total_orders': len(self.order_schedule),
            'orders_executed': self.orders_executed,
            'total_executed': self.total_executed,
            'progress': (self.orders_executed / len(self.order_schedule) * 100) if self.order_schedule else 0
        }


async def main():
    """主程序入口"""
    try:
        # 创建算法实例
        algorithm = BYBOrderAlgorithm()

        # 运行算法
        await algorithm.run_algorithm()

    except KeyboardInterrupt:
        logging.info("程序被用户中断")
        if 'algorithm' in locals():
            algorithm.stop_algorithm()
    except Exception as e:
        logging.error(f"主程序异常: {str(e)}\n{traceback.format_exc()}")


if __name__ == "__main__":
    print("=" * 60)
    print("BYB智能下单算法".center(60))
    print("=" * 60)
    print("功能: 从数据库获取参数，在指定时间范围内随机分布下单")
    print("下单间隔: 1-60分钟随机")
    print("=" * 60)

    # 运行主程序
    asyncio.run(main())
