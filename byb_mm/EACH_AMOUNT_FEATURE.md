# each_amount 参数功能说明

## 概述

已成功实现 `each_amount` 参数功能，单次最大下单量现在完全由数据库的 `each_amount` 字段决定，而不是硬编码的固定值。

## 功能特性

### 🎯 核心功能
- **数据库驱动**: 单次下单量上限由 `buy_back_params.each_amount` 字段控制
- **动态调整**: 参数变化时自动应用新的 `each_amount` 限制
- **智能分配**: 自动将订单大小控制在合理范围内
- **余额保护**: 确保不会超过 `each_amount` 限制，即使处理剩余金额

### 📊 实现逻辑

#### 1. 订单大小范围计算
```python
if each_amount is not None and each_amount > 0:
    max_order_size = each_amount
    min_order_size = min(200, each_amount * 0.5)  # 最小为each_amount的50%或200
else:
    max_order_size = 800  # 默认最大值
    min_order_size = 200  # 默认最小值
```

#### 2. 随机订单大小生成
```python
order_size = random.uniform(
    min_order_size,
    min(max_order_size, remaining_amount)
)
```

#### 3. 剩余金额处理
- 检查是否可以添加到最后一个订单而不超过 `each_amount`
- 如果会超过限制，创建新订单处理剩余金额
- 确保所有订单都不超过 `each_amount` 限制

## 使用示例

### 数据库配置
```sql
-- 设置单次最大下单量为500 BYB
INSERT INTO buy_back_params (days, total_amount, each_amount) 
VALUES (7.0, 50000.0, 500.0);

-- 修改单次最大下单量为300 BYB
UPDATE buy_back_params 
SET each_amount = 300.0, total_amount = 60000.0 
WHERE id = (SELECT MAX(id) FROM buy_back_params);
```

### 系统响应
1. **参数变化检测**: 系统检测到 `each_amount` 从 500 变为 300
2. **停止当前算法**: 如果有正在运行的算法，立即停止
3. **重新计算**: 根据新的 `each_amount=300` 重新生成订单计划
4. **执行新计划**: 所有新订单的大小都不会超过 300 BYB

## 测试验证

### 测试案例
```python
# 测试 each_amount=300 的限制
algorithm = BYBOrderAlgorithm()
schedule = algorithm.generate_order_schedule(0.1, 2000, 300)

# 验证结果
amounts = [order['amount'] for order in schedule]
max_amount = max(amounts)
assert max_amount <= 300, f"订单超过限制: {max_amount} > 300"
```

### 测试结果
```
生成订单数量: 8
订单金额: [248.55, 255.2, 206.24, 227.32, 280.46, 246.08, 300, 236.15]
总金额: 2000.00
最大订单: 300.00  ✅ 符合限制
最小订单: 206.24
each_amount限制验证: 通过
```

## 演示结果

### 演示参数
- `each_amount`: 500.0 BYB
- `total_amount`: 50000.0 BYB
- `days`: 7.0

### 执行结果
- **订单数量**: 100个订单
- **单次金额**: 每次都是 500.0 BYB（严格遵守 each_amount 限制）
- **总执行**: 50000.0 BYB
- **花费**: 4932.25 USDT

### 参数变化响应
当参数从 `(7天, 50000 BYB, 500 BYB)` 变为 `(10天, 70000 BYB, 500 BYB)` 时：
- ✅ 系统立即检测到变化
- ✅ 计算剩余需购买: 70000 - 4932.25 = 65067.75 BYB
- ✅ 检查余额充足性
- ✅ 保持 each_amount=500 的限制

## 代码修改点

### 1. 下单算法 (`byb_order_algorithm.py`)
- 修改 `generate_order_schedule()` 方法，增加 `each_amount` 参数
- 动态计算订单大小范围
- 智能处理剩余金额，确保不超过限制

### 2. 回购系统 (`byb_buy_back.py`)
- 在调用下单算法时传递 `each_amount` 参数
- 日志记录包含 `each_amount` 信息

### 3. 演示系统 (`demo_buy_back_system.py`)
- 模拟执行时使用 `each_amount` 参数
- 批次大小基于 `each_amount` 计算

## 配置说明

### 默认行为
- 如果 `each_amount` 为 `NULL` 或 `0`，使用默认范围 200-800 BYB
- 如果 `each_amount` 有效值，使用 `each_amount*0.5` 到 `each_amount` 的范围

### 最佳实践
1. **合理设置**: `each_amount` 应该根据市场流动性和风险控制需求设置
2. **动态调整**: 可以根据市场情况动态调整 `each_amount` 值
3. **监控验证**: 定期检查实际执行的订单大小是否符合预期

## 安全特性

### 风险控制
- **上限保护**: 严格限制单次下单量不超过 `each_amount`
- **下限保护**: 确保订单大小不会过小（最小200 BYB或each_amount的50%）
- **余额检查**: 处理剩余金额时仍然遵守 `each_amount` 限制

### 异常处理
- **参数验证**: 检查 `each_amount` 的有效性
- **边界处理**: 正确处理剩余金额和边界情况
- **日志记录**: 详细记录订单生成和限制应用过程

## 总结

✅ **完全实现**: 单次最大下单量现在完全由数据库 `each_amount` 参数决定
✅ **动态响应**: 参数变化时自动应用新的限制
✅ **严格控制**: 所有订单都严格遵守 `each_amount` 限制
✅ **智能处理**: 合理处理剩余金额，避免超限
✅ **测试验证**: 通过完整的测试验证功能正确性

这个功能增强了系统的灵活性和可控性，允许用户通过简单的数据库参数修改来精确控制下单策略，满足不同市场环境和风险管理需求。
