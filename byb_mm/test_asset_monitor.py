#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BYB资产监控测试脚本
用于测试资产获取和预警功能
"""

import asyncio
import logging
import sys
import pandas as pd
from datetime import datetime
import pytz

# 添加项目路径
sys.path.append(r'/home/<USER>/byb_mm/')

# 导入必要的模块
from byex.spot.trade import SpotTrade
import con_pri

# 设置北京时区
beijing_tz = pytz.timezone("Asia/Shanghai")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# 初始化API客户端
spot_client = SpotTrade(con_pri.api_key, con_pri.api_secret)


def test_get_usdt_assets():
    """测试获取USDT资产功能"""
    print("=" * 50)
    print("测试获取USDT资产功能")
    print("=" * 50)
    
    try:
        # 获取账户信息
        account = spot_client.account()
        print(f"账户信息获取成功: {type(account)}")
        
        if not account or 'coin_list' not in account:
            print("❌ 获取账户信息失败或数据格式异常")
            return None
            
        # 转换为DataFrame
        df_account = pd.DataFrame(account['coin_list'])
        print(f"账户币种数量: {len(df_account)}")
        print(f"可用币种: {df_account['coin'].tolist()}")
        
        # 查找USDT资产
        usdt_assets = df_account[df_account['coin'] == 'usdt']
        
        if usdt_assets.empty:
            print("❌ 未找到USDT资产信息")
            return None
            
        # 计算总资产
        normal_amount = float(usdt_assets['normal'].iloc[0])
        locked_amount = float(usdt_assets['locked'].iloc[0])
        total_amount = normal_amount + locked_amount
        
        print(f"✅ USDT资产详情:")
        print(f"   可用资产: {normal_amount:,.2f} USDT")
        print(f"   冻结资产: {locked_amount:,.2f} USDT")
        print(f"   总资产:   {total_amount:,.2f} USDT")
        
        # 检查预警状态
        threshold = 5000000  # 500万
        if total_amount < threshold:
            print(f"⚠️  资产预警: 当前资产 {total_amount:,.2f} < 阈值 {threshold:,.2f}")
            print(f"   资产不足: {threshold - total_amount:,.2f} USDT")
        else:
            print(f"✅ 资产正常: 当前资产 {total_amount:,.2f} >= 阈值 {threshold:,.2f}")
        
        return total_amount
        
    except Exception as e:
        print(f"❌ 获取USDT资产失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None


def test_all_assets():
    """测试获取所有资产信息"""
    print("\n" + "=" * 50)
    print("测试获取所有资产信息")
    print("=" * 50)
    
    try:
        account = spot_client.account()
        if not account or 'coin_list' not in account:
            print("❌ 获取账户信息失败")
            return
            
        df_account = pd.DataFrame(account['coin_list'])
        
        # 过滤出有余额的币种
        df_account['normal'] = df_account['normal'].astype(float)
        df_account['locked'] = df_account['locked'].astype(float)
        df_account['total'] = df_account['normal'] + df_account['locked']
        
        # 只显示总余额大于0的币种
        non_zero_assets = df_account[df_account['total'] > 0].copy()
        non_zero_assets = non_zero_assets.sort_values('total', ascending=False)
        
        print(f"有余额的币种数量: {len(non_zero_assets)}")
        print("\n资产详情:")
        print("-" * 60)
        print(f"{'币种':<10} {'可用':<15} {'冻结':<15} {'总计':<15}")
        print("-" * 60)
        
        for _, row in non_zero_assets.iterrows():
            coin = row['coin'].upper()
            normal = row['normal']
            locked = row['locked']
            total = row['total']
            print(f"{coin:<10} {normal:<15,.2f} {locked:<15,.2f} {total:<15,.2f}")
            
    except Exception as e:
        print(f"❌ 获取所有资产失败: {str(e)}")
        import traceback
        traceback.print_exc()


async def test_lark_notification():
    """测试Lark通知功能"""
    print("\n" + "=" * 50)
    print("测试Lark通知功能")
    print("=" * 50)
    
    # 导入监控脚本中的函数
    from byb_asset_monitor import send_lark, schedule_lark_message
    
    test_msg = (
        f"🧪 BYB资产监控测试\n"
        f"测试时间: {datetime.now(beijing_tz).strftime('%Y-%m-%d %H:%M:%S')}\n"
        f"这是一条测试消息，请忽略。"
    )
    
    try:
        print("发送测试消息到Lark...")
        schedule_lark_message(test_msg, level='INFO')
        print("✅ 测试消息发送完成")
        
        # 等待消息发送
        await asyncio.sleep(3)
        
    except Exception as e:
        print(f"❌ Lark通知测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


async def main():
    """主测试函数"""
    print("BYB资产监控功能测试")
    print("=" * 50)
    
    # 测试资产获取
    usdt_amount = test_get_usdt_assets()
    
    # 测试所有资产
    test_all_assets()
    
    # 测试Lark通知
    await test_lark_notification()
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)
    
    if usdt_amount is not None:
        print(f"当前USDT总资产: {usdt_amount:,.2f}")
        threshold = 5000000
        if usdt_amount < threshold:
            print(f"⚠️  需要预警: 资产不足 {threshold - usdt_amount:,.2f} USDT")
        else:
            print("✅ 资产充足，无需预警")
    else:
        print("❌ 无法获取资产信息")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
