import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def detect_and_optimize_spikes(df, spike_threshold=3.0):
    """
    检测并优化插针数据
    Args:
        df: K线数据DataFrame
        spike_threshold: 插针检测阈值（标准差倍数）
    Returns:
        优化后的DataFrame
    """
    df_optimized = df.copy()
    
    # 计算价格的移动平均和标准差
    window = min(20, len(df) // 4)  # 动态窗口大小
    df_optimized['price_ma'] = df_optimized['colse 收盘价'].rolling(window=window, center=True).mean()
    df_optimized['price_std'] = df_optimized['colse 收盘价'].rolling(window=window, center=True).std()
    
    # 填充边界值
    df_optimized['price_ma'] = df_optimized['price_ma'].fillna(method='bfill').fillna(method='ffill')
    df_optimized['price_std'] = df_optimized['price_std'].fillna(method='bfill').fillna(method='ffill')
    
    spike_count = 0
    
    for i in range(len(df_optimized)):
        row = df_optimized.iloc[i]
        ma = row['price_ma']
        std = row['price_std']
        
        # 检测高价插针
        if row['high 最高价'] > ma + spike_threshold * std:
            # 优化高价：使用前后K线的高价平均值或移动平均值
            if i > 0 and i < len(df_optimized) - 1:
                prev_high = df_optimized.iloc[i-1]['high 最高价']
                next_high = df_optimized.iloc[i+1]['high 最高价']
                optimized_high = min(row['high 最高价'], max(prev_high, next_high, ma + 2 * std))
            else:
                optimized_high = min(row['high 最高价'], ma + 2 * std)

            df_optimized.iloc[i, df_optimized.columns.get_loc('high 最高价')] = optimized_high
            spike_count += 1
            print(f"优化上插针 - 时间: {datetime.fromtimestamp(row['时间戳(秒)'])}, "
                  f"原高价: {row['high 最高价']:.6f}, 优化后: {optimized_high:.6f}")

        # 检测低价插针
        if row['low最低价'] < ma - spike_threshold * std:
            # 优化低价：使用前后K线的低价平均值或移动平均值
            if i > 0 and i < len(df_optimized) - 1:
                prev_low = df_optimized.iloc[i-1]['low最低价']
                next_low = df_optimized.iloc[i+1]['low最低价']
                optimized_low = max(row['low最低价'], min(prev_low, next_low, ma - 2 * std))
            else:
                optimized_low = max(row['low最低价'], ma - 2 * std)

            df_optimized.iloc[i, df_optimized.columns.get_loc('low最低价')] = optimized_low
            spike_count += 1
            print(f"优化下插针 - 时间: {datetime.fromtimestamp(row['时间戳(秒)'])}, "
                  f"原低价: {row['low最低价']:.6f}, 优化后: {optimized_low:.6f}")
    
    print(f"总共优化了 {spike_count} 个插针")
    
    # 清理临时列
    df_optimized = df_optimized.drop(['price_ma', 'price_std'], axis=1)
    
    return df_optimized

def plot_candlestick_chart(df, title="BYBUSDT 1分钟K线图", optimize_spikes=True, spike_threshold=3.0):
    """
    绘制K线图
    Args:
        df: K线数据DataFrame
        title: 图表标题
        optimize_spikes: 是否优化插针
        spike_threshold: 插针检测阈值
    """
    # 数据预处理
    df_plot = df.copy()
    
    # 转换时间戳为datetime
    df_plot['datetime'] = pd.to_datetime(df_plot['时间戳(秒)'], unit='s')
    
    # 插针优化
    if optimize_spikes:
        print("正在检测和优化插针...")
        df_plot = detect_and_optimize_spikes(df_plot, spike_threshold)
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12), 
                                   gridspec_kw={'height_ratios': [3, 1]})
    
    # 主图：K线图
    for i in range(len(df_plot)):
        row = df_plot.iloc[i]
        x = row['datetime']
        open_price = row['open 开盘价']
        high_price = row['high 最高价']
        low_price = row['low最低价']
        close_price = row['colse 收盘价']
        
        # 确定颜色：红涨绿跌
        color = 'red' if close_price >= open_price else 'green'
        
        # 绘制影线
        ax1.plot([x, x], [low_price, high_price], color='black', linewidth=0.8)
        
        # 绘制实体
        body_height = abs(close_price - open_price)
        body_bottom = min(open_price, close_price)
        
        if body_height > 0:
            rect = Rectangle((mdates.date2num(x) - 0.0003, body_bottom), 
                           0.0006, body_height, 
                           facecolor=color, edgecolor='black', linewidth=0.5, alpha=0.8)
            ax1.add_patch(rect)
        else:
            # 十字星
            ax1.plot([mdates.date2num(x) - 0.0003, mdates.date2num(x) + 0.0003], 
                    [open_price, open_price], color='black', linewidth=1)
    
    # 设置主图
    ax1.set_title(title, fontsize=16, fontweight='bold', pad=20)
    ax1.set_ylabel('价格 (USDT)', fontsize=12)
    ax1.grid(True, alpha=0.3)
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
    ax1.xaxis.set_major_locator(mdates.HourLocator(interval=2))
    
    # 添加价格统计信息
    price_info = f"开盘: {df_plot['open 开盘价'].iloc[0]:.6f} | " \
                f"收盘: {df_plot['colse 收盘价'].iloc[-1]:.6f} | " \
                f"最高: {df_plot['high 最高价'].max():.6f} | " \
                f"最低: {df_plot['low最低价'].min():.6f}"
    ax1.text(0.02, 0.98, price_info, transform=ax1.transAxes, 
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.7),
            verticalalignment='top', fontsize=10)
    
    # 副图：交易量
    colors = ['red' if df_plot.iloc[i]['colse 收盘价'] >= df_plot.iloc[i]['open 开盘价']
              else 'green' for i in range(len(df_plot))]
    
    ax2.bar(df_plot['datetime'], df_plot['vol交易量'], 
           color=colors, alpha=0.7, width=pd.Timedelta(minutes=0.8))
    
    ax2.set_ylabel('交易量 (BYB)', fontsize=12)
    ax2.set_xlabel('时间', fontsize=12)
    ax2.grid(True, alpha=0.3)
    ax2.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
    ax2.xaxis.set_major_locator(mdates.HourLocator(interval=2))
    
    # 旋转x轴标签
    plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)
    plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    filename = f'bybusdt_kline_chart_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"K线图已保存为: {filename}")
    
    # 显示图表
    plt.show()
    
    return df_plot

def analyze_kline_data(df):
    """分析K线数据并输出统计信息"""
    print("\n=== BYBUSDT K线数据分析 ===")
    print(f"数据时间范围: {datetime.fromtimestamp(df['时间戳(秒)'].min())} 到 {datetime.fromtimestamp(df['时间戳(秒)'].max())}")
    print(f"K线总数: {len(df)} 根")
    
    # 价格统计
    print(f"\n价格统计:")
    print(f"开盘价: {df['open 开盘价'].iloc[0]:.6f} USDT")
    print(f"收盘价: {df['colse 收盘价'].iloc[-1]:.6f} USDT")
    print(f"最高价: {df['high 最高价'].max():.6f} USDT")
    print(f"最低价: {df['low最低价'].min():.6f} USDT")
    print(f"价格振幅: {((df['high 最高价'].max() - df['low最低价'].min()) / df['low最低价'].min() * 100):.2f}%")
    
    # 涨跌统计
    up_count = (df['colse 收盘价'] > df['open 开盘价']).sum()
    down_count = (df['colse 收盘价'] < df['open 开盘价']).sum()
    flat_count = (df['colse 收盘价'] == df['open 开盘价']).sum()
    
    print(f"\n涨跌统计:")
    print(f"上涨K线: {up_count} 根 ({up_count/len(df)*100:.1f}%)")
    print(f"下跌K线: {down_count} 根 ({down_count/len(df)*100:.1f}%)")
    print(f"平盘K线: {flat_count} 根 ({flat_count/len(df)*100:.1f}%)")
    
    # 交易量统计
    print(f"\n交易量统计:")
    print(f"总交易量: {df['vol交易量'].sum():.2f} BYB")
    print(f"总交易额: {df['amount交易额'].sum():.2f} USDT")
    print(f"平均每分钟交易量: {df['vol交易量'].mean():.2f} BYB")
    print(f"最大单分钟交易量: {df['vol交易量'].max():.2f} BYB")

def main():
    """主函数"""
    try:
        # 读取K线数据，尝试不同的编码
        print("正在读取K线数据...")
        try:
            df = pd.read_csv('bybusdt_1min_kline_final.csv', encoding='utf-8')
        except UnicodeDecodeError:
            try:
                df = pd.read_csv('bybusdt_1min_kline_final.csv', encoding='gbk')
            except UnicodeDecodeError:
                df = pd.read_csv('bybusdt_1min_kline_final.csv', encoding='latin-1')
        print(f"成功读取 {len(df)} 条K线数据")
        
        # 数据分析
        analyze_kline_data(df)
        
        # 绘制K线图（带插针优化）
        print("\n正在绘制K线图...")
        df_optimized = plot_candlestick_chart(
            df, 
            title="BYBUSDT 1分钟K线图 (已优化插针)", 
            optimize_spikes=True, 
            spike_threshold=2.5  # 可调整插针检测敏感度
        )
        
        # 保存优化后的数据
        output_file = 'bybusdt_1min_kline.csv'
        df_optimized.drop('datetime', axis=1).to_csv(output_file, index=False)
        print(f"优化后的K线数据已保存为: {output_file}")
        
    except FileNotFoundError:
        print("错误: 未找到文件 'bybusdt_1min_kline_final.csv'")
        print("请确保文件在当前目录下")
    except Exception as e:
        print(f"发生错误: {e}")

if __name__ == "__main__":
    main()
