#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BYB资产监控脚本
监控BYB资产，当资产小于500万时通过Lark发送预警
"""

import asyncio
import logging
import pandas as pd
import sys
import time
from datetime import datetime, timedelta
import pytz
import json
import os
import traceback
import backoff
import aiohttp

# 添加项目路径
sys.path.append(r'/home/<USER>/byb_mm/')

# 导入必要的模块
from byex.spot.trade import SpotTrade
import con_pri

# 设置北京时区
beijing_tz = pytz.timezone("Asia/Shanghai")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('byb_asset_monitor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# 初始化API客户端
spot_client = SpotTrade(con_pri.api_key, con_pri.api_secret)

# 监控配置
ASSET_THRESHOLD = 5000000  # 500万BYB预警阈值
CHECK_INTERVAL = 600  # 检查间隔（秒），默认10分钟
ALERT_COOLDOWN = 3600  # 预警冷却时间（秒），默认1小时，避免频繁预警

# 全局状态
last_alert_time = None
last_asset_amount = None


@backoff.on_exception(backoff.expo, Exception, max_tries=3)
async def send_lark(msg, level='warning'):
    """发送消息到Lark（飞书）"""
    headers = {'Content-Type': 'application/json'}
    payload_message = {
        "msg_type": "text",
        "content": {
            "text": f"【{level}】: {str(msg)}"
        }
    }
    webhook = 'https://open.larksuite.com/open-apis/bot/v2/hook/9363fad2-b56d-41a0-aa24-1827977e1a3c'
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(webhook, data=json.dumps(payload_message), headers=headers, timeout=10) as response:
                res = await response.json()
                statuscode = res.get('StatusCode', 404)
                if statuscode != 0 or res["StatusMessage"] != 'success':
                    logging.error(f"Lark webhook response error: {res}")
    except Exception as e:
        logging.error(f"Lark notification error for message '{msg}': {str(e)}")


def schedule_lark_message(msg, level='warning'):
    """在当前事件循环中调度Lark消息发送任务，不阻塞主程序"""
    try:
        # 获取当前事件循环
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # 如果事件循环正在运行，创建一个任务但不等待
            task = loop.create_task(send_lark(msg, level))
            # 添加错误处理回调，避免未处理的异常
            task.add_done_callback(lambda t: None if not t.exception() else
                                 logging.error(f"Lark消息发送任务失败: {t.exception()}"))
        else:
            # 如果没有运行的事件循环，使用asyncio.run
            asyncio.run(send_lark(msg, level))
    except Exception as e:
        logging.error(f"调度Lark消息失败: {str(e)}")


def get_byb_assets():
    """获取BYB资产数据"""
    try:
        account = spot_client.account()
        if not account or 'coin_list' not in account:
            logging.error("获取账户信息失败或数据格式异常")
            return None
            
        df_account = pd.DataFrame(account['coin_list'])
        byb_assets = df_account[df_account['coin'] == 'byb']
        
        if byb_assets.empty:
            logging.error("未找BYB资产信息")
            return None
            
        # 计算总资产（可用 + 冻结）
        normal_amount = float(byb_assets['normal'].iloc[0])
        locked_amount = float(byb_assets['locked'].iloc[0])
        total_amount = normal_amount + locked_amount
        
        logging.info(f"BYB资产详情 - 可用: {normal_amount:,.2f}, 冻结: {locked_amount:,.2f}, 总计: {total_amount:,.2f}")
        return total_amount
        
    except Exception as e:
        logging.error(f"获取BYB资产失败: {str(e)}\n{traceback.format_exc()}")
        return None


def should_send_alert(current_amount):
    """判断是否应该发送预警"""
    global last_alert_time, last_asset_amount
    
    current_time = datetime.now(beijing_tz)
    
    # 如果资产大于等于阈值，重置预警状态
    if current_amount >= ASSET_THRESHOLD:
        if last_alert_time is not None:
            logging.info(f"资产已恢复到安全水平: {current_amount:,.2f} BYB >= {ASSET_THRESHOLD:,.2f} BYB")
            last_alert_time = None
        return False
    
    # 如果从未发送过预警，或者距离上次预警超过冷却时间
    if last_alert_time is None or (current_time - last_alert_time).total_seconds() > ALERT_COOLDOWN:
        return True
    
    return False


async def check_asset_and_alert():
    """检查资产并发送预警"""
    global last_alert_time, last_asset_amount
    
    try:
        current_amount = get_byb_assets()
        
        if current_amount is None:
            logging.warning("无法获取资产信息，跳过本次检查")
            return
        
        last_asset_amount = current_amount
        
        # 检查是否需要预警
        if current_amount < ASSET_THRESHOLD:
            if should_send_alert(current_amount):
                alert_msg = (
                    f"🚨 BYB资产预警 🚨\n"
                    f"当前BYB资产: {current_amount:,.2f}\n"
                    f"预警阈值: {ASSET_THRESHOLD:,.2f}\n"
                    f"资产不足: {ASSET_THRESHOLD - current_amount:,.2f}\n"
                    f"检查时间: {datetime.now(beijing_tz).strftime('%Y-%m-%d %H:%M:%S')}\n"
                    f"请及时补充资产！"
                )
                
                logging.warning(f"发送资产预警: 当前资产 {current_amount:,.2f} < 阈值 {ASSET_THRESHOLD:,.2f}")
                schedule_lark_message(alert_msg, level='ERROR')
                last_alert_time = datetime.now(beijing_tz)
            else:
                logging.info(f"资产不足但在冷却期内，不重复预警: {current_amount:,.2f}")
        else:
            logging.info(f"资产正常: {current_amount:,.2f} >= {ASSET_THRESHOLD:,.2f}")
            
    except Exception as e:
        error_msg = f"资产检查过程中发生错误: {str(e)}"
        logging.error(f"{error_msg}\n{traceback.format_exc()}")
        schedule_lark_message(error_msg, level='ERROR')


async def send_startup_notification():
    """发送启动通知"""
    startup_msg = (
        f"📊 BYB资产监控启动\n"
        f"预警阈值: {ASSET_THRESHOLD:,.2f} BYB\n"
        f"检查间隔: {CHECK_INTERVAL} 秒\n"
        f"预警冷却: {ALERT_COOLDOWN} 秒\n"
        f"启动时间: {datetime.now(beijing_tz).strftime('%Y-%m-%d %H:%M:%S')}"
    )
    logging.info("BYB资产监控启动")
    schedule_lark_message(startup_msg, level='INFO')


async def main():
    """主监控循环"""
    # 发送启动通知
    await send_startup_notification()
    
    # 等待一下让启动通知发送完成
    await asyncio.sleep(2)
    
    logging.info(f"开始监控BYB资产，预警阈值: {ASSET_THRESHOLD:,.2f} BYB，检查间隔: {CHECK_INTERVAL} 秒")
    
    while True:
        try:
            await check_asset_and_alert()
            
            # 等待下次检查
            await asyncio.sleep(CHECK_INTERVAL)
            
        except KeyboardInterrupt:
            logging.info("收到中断信号，正在停止监控...")
            break
        except Exception as e:
            error_msg = f"主循环发生未预期错误: {str(e)}"
            logging.error(f"{error_msg}\n{traceback.format_exc()}")
            schedule_lark_message(error_msg, level='ERROR')
            
            # 发生错误后等待一段时间再继续
            await asyncio.sleep(60)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logging.info("程序被用户中断")
    except Exception as e:
        logging.error(f"程序启动失败: {str(e)}\n{traceback.format_exc()}")
